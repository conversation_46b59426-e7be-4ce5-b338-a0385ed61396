import { pgTable, text, varchar, serial, integer, boolean, timestamp, decimal, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  email: text("email").unique(),
  phone: text("phone"),
  username: text("username"),
  firstName: text("first_name"),
  lastName: text("last_name"),
  role: text("role").default("guest"), // guest, admin, artist
  passwordHash: text("password_hash"),
  isGuest: boolean("is_guest").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

export const venues = pgTable("venues", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address").notNull(),
  wifiSSID: text("wifi_ssid"),
  isActive: boolean("is_active").default(true),
});

export const events = pgTable("events", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").references(() => venues.id),
  name: text("name").notNull(),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  orderCutoff: timestamp("order_cutoff").notNull(),
  isActive: boolean("is_active").default(true),
});

export const menuCategories = pgTable("menu_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // 'food', 'drinks', 'merch'
  eventId: integer("event_id").references(() => events.id),
});

export const menuItems = pgTable("menu_items", {
  id: serial("id").primaryKey(),
  categoryId: integer("category_id").references(() => menuCategories.id),
  name: text("name").notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  stock: integer("stock").default(0),
  prepTime: integer("prep_time").default(5), // minutes
  imageUrl: text("image_url"),
  isAvailable: boolean("is_available").default(true),
});

export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  eventId: integer("event_id").references(() => events.id),
  status: text("status").notNull().default('confirmed'), // 'confirmed', 'preparing', 'ready', 'completed', 'cancelled'
  totalAmount: decimal("total_amount", { precision: 10, scale: 2 }).notNull(),
  pickupTime: timestamp("pickup_time").notNull(),
  pickupLocation: text("pickup_location").default('Booth #3'),
  paymentIntentId: text("payment_intent_id"),
  qrCode: text("qr_code"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const orderItems = pgTable("order_items", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").references(() => orders.id),
  menuItemId: integer("menu_item_id").references(() => menuItems.id),
  quantity: integer("quantity").notNull(),
  unitPrice: decimal("unit_price", { precision: 10, scale: 2 }).notNull(),
  customizations: jsonb("customizations"), // JSON for custom options
});

export const artistMerch = pgTable("artist_merch", {
  id: serial("id").primaryKey(),
  artistId: integer("artist_id").references(() => users.id),
  eventId: integer("event_id").references(() => events.id),
  name: text("name").notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  stock: integer("stock").default(0),
  imageUrl: text("image_url"),
  isBundle: boolean("is_bundle").default(false),
  bundleItems: jsonb("bundle_items"), // JSON array of item IDs
});

export const queueStatus = pgTable("queue_status", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  preparing: integer("preparing").default(0),
  ready: integer("ready").default(0),
  avgWaitTime: integer("avg_wait_time").default(0), // minutes
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Tickets table for event tickets
export const tickets = pgTable("tickets", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  type: text("type"), // "general", "vip", "premium"
  price: decimal("price", { precision: 10, scale: 2 }),
  totalQuantity: integer("total_quantity"),
  availableQuantity: integer("available_quantity"),
  description: text("description"),
  seatSection: text("seat_section"),
  isActive: boolean("is_active").default(true),
});

// Pre-event orders table
export const preEventOrders = pgTable("pre_event_orders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  eventId: integer("event_id").references(() => events.id),
  status: text("status").default("pending"), // "pending", "confirmed", "cancelled", "completed"
  totalAmount: decimal("total_amount", { precision: 10, scale: 2 }),
  pickupTime: timestamp("pickup_time"),
  canModifyUntil: timestamp("can_modify_until"), // 30 minutes before pickup
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Pre-event order items (tickets, food, merch)
export const preEventOrderItems = pgTable("pre_event_order_items", {
  id: serial("id").primaryKey(),
  preEventOrderId: integer("pre_event_order_id").references(() => preEventOrders.id),
  itemType: text("item_type"), // "ticket", "food", "merch"
  itemId: integer("item_id"), // references tickets.id, menuItems.id, or artistMerch.id
  quantity: integer("quantity"),
  unitPrice: decimal("unit_price", { precision: 10, scale: 2 }),
  customizations: jsonb("customizations"),
  status: text("status").default("active"), // "active", "cancelled"
});

// =============================================================================
// AI-POWERED FEATURES & OPERATIONAL MANAGEMENT
// =============================================================================

// Time windows for pickup scheduling
export const timeWindows = pgTable("time_windows", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  maxOrders: integer("max_orders").default(50), // AI-calculated capacity
  currentOrders: integer("current_orders").default(0),
  isAvailable: boolean("is_available").default(true),
  staffRequired: integer("staff_required").default(2), // AI recommendation
  createdAt: timestamp("created_at").defaultNow(),
});

// Staff scheduling and AI recommendations
export const staffSchedule = pgTable("staff_schedule", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  timeWindowId: integer("time_window_id").references(() => timeWindows.id),
  staffType: text("staff_type").notNull(), // "kitchen", "prep", "runner", "cashier"
  requiredCount: integer("required_count").notNull(),
  scheduledCount: integer("scheduled_count").default(0),
  aiRecommendation: jsonb("ai_recommendation"), // AI insights and reasoning
  isOptimal: boolean("is_optimal").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Inventory forecasting and AI predictions
export const inventoryForecasts = pgTable("inventory_forecasts", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  menuItemId: integer("menu_item_id").references(() => menuItems.id),
  predictedDemand: integer("predicted_demand").notNull(),
  currentStock: integer("current_stock").notNull(),
  recommendedStock: integer("recommended_stock").notNull(),
  confidence: decimal("confidence", { precision: 5, scale: 2 }), // AI confidence %
  factors: jsonb("factors"), // Weather, historical data, event type, etc.
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Waste tracking and analytics
export const wasteTracking = pgTable("waste_tracking", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  menuItemId: integer("menu_item_id").references(() => menuItems.id),
  preparedQuantity: integer("prepared_quantity").notNull(),
  soldQuantity: integer("sold_quantity").notNull(),
  wastedQuantity: integer("wasted_quantity").notNull(),
  wasteReason: text("waste_reason"), // "overprep", "quality", "expired", "cancelled_orders"
  costImpact: decimal("cost_impact", { precision: 10, scale: 2 }),
  createdAt: timestamp("created_at").defaultNow(),
});

// Loyalty system and post-event offers
export const loyaltyProgram = pgTable("loyalty_program", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  totalSpent: decimal("total_spent", { precision: 10, scale: 2 }).default("0"),
  totalOrders: integer("total_orders").default(0),
  loyaltyPoints: integer("loyalty_points").default(0),
  tier: text("tier").default("bronze"), // "bronze", "silver", "gold", "platinum"
  lastEventId: integer("last_event_id").references(() => events.id),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Post-event offers and AI-generated promotions
export const postEventOffers = pgTable("post_event_offers", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  eventId: integer("event_id").references(() => events.id),
  offerType: text("offer_type").notNull(), // "discount", "free_item", "early_access", "bundle"
  offerValue: decimal("offer_value", { precision: 10, scale: 2 }),
  description: text("description").notNull(),
  validUntil: timestamp("valid_until").notNull(),
  isUsed: boolean("is_used").default(false),
  aiGenerated: boolean("ai_generated").default(true),
  targetingReason: jsonb("targeting_reason"), // Why AI chose this offer
  createdAt: timestamp("created_at").defaultNow(),
});

// Vendor/contractor management
export const vendors = pgTable("vendors", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // "food", "beverage", "merch", "security"
  contactEmail: text("contact_email"),
  contactPhone: text("contact_phone"),
  isActive: boolean("is_active").default(true),
  commissionRate: decimal("commission_rate", { precision: 5, scale: 2 }), // Percentage
  createdAt: timestamp("created_at").defaultNow(),
});

// Vendor assignments to events
export const vendorAssignments = pgTable("vendor_assignments", {
  id: serial("id").primaryKey(),
  vendorId: integer("vendor_id").references(() => vendors.id),
  eventId: integer("event_id").references(() => events.id),
  boothLocation: text("booth_location"),
  setupTime: timestamp("setup_time"),
  teardownTime: timestamp("teardown_time"),
  isActive: boolean("is_active").default(true),
});

// =============================================================================
// ZOD VALIDATION SCHEMAS
// =============================================================================
// These schemas validate data before it goes into the database
// They help prevent errors and ensure data integrity

// Core entity schemas
export const insertUserSchema = createInsertSchema(users).omit({ id: true, createdAt: true });
export const insertVenueSchema = createInsertSchema(venues).omit({ id: true });
export const insertEventSchema = createInsertSchema(events).omit({ id: true });
export const insertMenuCategorySchema = createInsertSchema(menuCategories).omit({ id: true });
export const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });
export const insertOrderSchema = createInsertSchema(orders).omit({ id: true, createdAt: true, updatedAt: true });
export const insertOrderItemSchema = createInsertSchema(orderItems).omit({ id: true });
export const insertArtistMerchSchema = createInsertSchema(artistMerch).omit({ id: true });
export const insertQueueStatusSchema = createInsertSchema(queueStatus).omit({ id: true, updatedAt: true });
export const insertTicketSchema = createInsertSchema(tickets).omit({ id: true });
export const insertPreEventOrderSchema = createInsertSchema(preEventOrders).omit({ id: true, createdAt: true, updatedAt: true });
export const insertPreEventOrderItemSchema = createInsertSchema(preEventOrderItems).omit({ id: true });

// AI and operational schemas
export const insertTimeWindowSchema = createInsertSchema(timeWindows).omit({ id: true, createdAt: true });
export const insertStaffScheduleSchema = createInsertSchema(staffSchedule).omit({ id: true, createdAt: true, updatedAt: true });
export const insertInventoryForecastSchema = createInsertSchema(inventoryForecasts).omit({ id: true, createdAt: true, updatedAt: true });
export const insertWasteTrackingSchema = createInsertSchema(wasteTracking).omit({ id: true, createdAt: true });
export const insertLoyaltyProgramSchema = createInsertSchema(loyaltyProgram).omit({ id: true, createdAt: true, updatedAt: true });
export const insertPostEventOfferSchema = createInsertSchema(postEventOffers).omit({ id: true, createdAt: true });
export const insertVendorSchema = createInsertSchema(vendors).omit({ id: true, createdAt: true });
export const insertVendorAssignmentSchema = createInsertSchema(vendorAssignments).omit({ id: true });

// =============================================================================
// TYPESCRIPT TYPES
// =============================================================================
// These types provide full TypeScript support throughout the application
// They're automatically generated from the database schema

// Core entity types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Venue = typeof venues.$inferSelect;
export type InsertVenue = z.infer<typeof insertVenueSchema>;
export type Event = typeof events.$inferSelect;
export type InsertEvent = z.infer<typeof insertEventSchema>;
export type MenuCategory = typeof menuCategories.$inferSelect;
export type InsertMenuCategory = z.infer<typeof insertMenuCategorySchema>;
export type MenuItem = typeof menuItems.$inferSelect;
export type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;
export type Order = typeof orders.$inferSelect;
export type InsertOrder = z.infer<typeof insertOrderSchema>;
export type OrderItem = typeof orderItems.$inferSelect;
export type InsertOrderItem = z.infer<typeof insertOrderItemSchema>;
export type ArtistMerch = typeof artistMerch.$inferSelect;
export type InsertArtistMerch = z.infer<typeof insertArtistMerchSchema>;
export type QueueStatus = typeof queueStatus.$inferSelect;
export type InsertQueueStatus = z.infer<typeof insertQueueStatusSchema>;
export type Ticket = typeof tickets.$inferSelect;
export type InsertTicket = z.infer<typeof insertTicketSchema>;
export type PreEventOrder = typeof preEventOrders.$inferSelect;
export type InsertPreEventOrder = z.infer<typeof insertPreEventOrderSchema>;
export type PreEventOrderItem = typeof preEventOrderItems.$inferSelect;
export type InsertPreEventOrderItem = z.infer<typeof insertPreEventOrderItemSchema>;

// AI and operational types
export type TimeWindow = typeof timeWindows.$inferSelect;
export type InsertTimeWindow = z.infer<typeof insertTimeWindowSchema>;
export type StaffSchedule = typeof staffSchedule.$inferSelect;
export type InsertStaffSchedule = z.infer<typeof insertStaffScheduleSchema>;
export type InventoryForecast = typeof inventoryForecasts.$inferSelect;
export type InsertInventoryForecast = z.infer<typeof insertInventoryForecastSchema>;
export type WasteTracking = typeof wasteTracking.$inferSelect;
export type InsertWasteTracking = z.infer<typeof insertWasteTrackingSchema>;
export type LoyaltyProgram = typeof loyaltyProgram.$inferSelect;
export type InsertLoyaltyProgram = z.infer<typeof insertLoyaltyProgramSchema>;
export type PostEventOffer = typeof postEventOffers.$inferSelect;
export type InsertPostEventOffer = z.infer<typeof insertPostEventOfferSchema>;
export type Vendor = typeof vendors.$inferSelect;
export type InsertVendor = z.infer<typeof insertVendorSchema>;
export type VendorAssignment = typeof vendorAssignments.$inferSelect;
export type InsertVendorAssignment = z.infer<typeof insertVendorAssignmentSchema>;

// =============================================================================
// UTILITY TYPES FOR COMPLEX OPERATIONS
// =============================================================================

// Complete order with all related data
export type OrderWithItems = Order & {
  items: (OrderItem & { menuItem: MenuItem })[];
  user: User;
  event: Event;
};

// Event with full details for display
export type EventWithDetails = Event & {
  venue: Venue;
  menuCategories: (MenuCategory & { items: MenuItem[] })[];
  timeWindows: TimeWindow[];
  tickets: Ticket[];
};

// AI insights for admin dashboard
export type AIInsights = {
  demandForecast: InventoryForecast[];
  staffRecommendations: StaffSchedule[];
  wasteAnalysis: WasteTracking[];
  loyaltyTrends: {
    newMembers: number;
    averageSpend: number;
    retentionRate: number;
  };
};
