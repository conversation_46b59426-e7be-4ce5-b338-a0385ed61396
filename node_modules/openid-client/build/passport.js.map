{"version": 3, "file": "passport.js", "sourceRoot": "", "sources": ["../src/passport.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,MAAM,YAAY,CAAA;AA6FpC,MAAM,OAAO,QAAQ;IAIV,IAAI,CAAQ;IAIrB,OAAO,CAAsB;IAI7B,OAAO,CAA4C;IAInD,YAAY,CAAS;IAIrB,WAAW,CAAQ;IAInB,kBAAkB,CAAU;IAI5B,MAAM,CAAU;IAIhB,OAAO,CAAU;IAIjB,OAAO,CAAgC;IAIvC,KAAK,CAA8B;IAInC,MAAM,CAAS;IAOf,YACE,OAAqD,EACrD,MAAkD;QAElD,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,YAAY,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,SAAS,EAAE,CAAA;QACvB,CAAC;QAED,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;YACjC,MAAM,IAAI,SAAS,EAAE,CAAA;QACvB,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAA;QAEhE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAA;QAChC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAA;QAC7C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAA;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAA;QAC7B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAA;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAA;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAA;QAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QACrB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAA;QACvC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAA;IACrD,CAAC;IAMD,0BAA0B,CAKxB,GAAoB,EAAE,OAAiB;QAEvC,IAAI,MAAM,GAAG,IAAI,eAAe,EAAE,CAAA;QAElC,IAAI,OAAO,EAAE,KAAK,EAAE,CAAC;YACnB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC1D,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YAC9C,CAAC;iBAAM,IAAI,OAAO,OAAO,EAAE,KAAK,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACtE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;YACpC,CAAC;QACH,CAAC;QAED,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;YACpB,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QACtC,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAOD,gCAAgC,CAK9B,GAAoB,EAAE,OAAiB;QAEvC,OAAO,EAAE,CAAA;IACX,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAQxB,GAAoB,EACpB,OAAiB;QAEjB,IAAI,CAAC;YACH,IAAI,UAAU,GAAG,MAAM,CAAC,qBAAqB,CAC3C,IAAI,CAAC,OAAO,EACZ,IAAI,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CACnE,CAAA;YAED,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvE,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAA;YAC5D,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,CAAC,sBAAsB,EAAE,CAAA;YACpD,MAAM,cAAc,GAClB,MAAM,MAAM,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;YACvD,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAA;YAC7D,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAA;YAE5D,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,YAAY,EAAE;gBAC7C,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EACrC,CAAC;gBACD,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAA;YAC5D,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;gBACtE,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;YAChE,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzD,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;YACnD,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;YAEpC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBACrD,UAAU,CAAC,YAAY,CAAC,GAAG,CACzB,UAAU,EACV,MAAM,IAAI,CAAC,mBAAmB,EAAE,CACjC,CAAA;YACH,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAA;YACnC,MAAM,SAAS,GAAc,EAAE,aAAa,EAAE,YAAY,EAAE,CAAA;YAE5D,IAAI,KAAoB,CAAA;YACxB,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBACnD,SAAS,CAAC,KAAK,GAAG,KAAK,CAAA;YACzB,CAAC;YACD,IAAI,KAAoB,CAAA;YACxB,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBACnD,SAAS,CAAC,KAAK,GAAG,KAAK,CAAA;YACzB,CAAC;YACD,IAAI,OAAsB,CAAA;YAC1B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACvD,SAAS,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YAC3C,CAAC;YAED,CAAC;YAAC,GAAW,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,SAAS,CAAA;YAE7C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,IAAI,GAAyC,CAAA;gBAC7C,IAAI,eAA2D,CAAA;gBAC/D,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAChC,CAAC;oBAAA,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,OAAO,CAAA;gBACxC,CAAC;qBAAM,CAAC;oBACN,GAAG,GAAG,IAAI,CAAC,OAAO,CAAA;gBACpB,CAAC;gBACD,UAAU,GAAG,MAAM,MAAM,CAAC,4BAA4B,CACpD,IAAI,CAAC,OAAO,EACZ,UAAU,CAAC,YAAY,EACvB,GAAG,EACH,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,eAAe,EAAE,CAC9C,CAAA;YACH,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,UAAU,GAAG,MAAM,MAAM,CAAC,4BAA4B,CACpD,IAAI,CAAC,OAAO,EACZ,UAAU,CAAC,YAAY,EACvB,EAAE,IAAI,EAAE,CACT,CAAA;YACH,CAAC;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QACvC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAQ1B,GAAoB,EACpB,UAAe,EACf,OAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAA;YACnC,MAAM,SAAS,GAAe,GAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;YAE7D,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,IAAI,CAAC;oBACf,OAAO,EAAE,8CAA8C;iBACxD,CAAC,CAAA;YACJ,CAAC;YAED,IAAI,KAAK,GAAkB,UAAU,CAAA;YACrC,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC1B,KAAK,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;oBACnC,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE;wBACrB,KAAK,MAAM,KAAK,IAAI,MAAO,EAAE,CAAC;4BAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;wBACxB,CAAC;wBACD,OAAO,GAAG,CAAA;oBACZ,CAAC,EACD,IAAI,OAAO,EAAE,CACd;oBACD,IAAI,EAAE,GAAG;oBACT,MAAM,EAAE,MAAM;iBACf,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAChD,IAAI,CAAC,OAAO,EACZ,KAAK,EACL;gBACE,gBAAgB,EAAE,SAAS,CAAC,aAAa;gBACzC,aAAa,EAAE,SAAS,CAAC,KAAK;gBAC9B,aAAa,EAAE,SAAS,CAAC,KAAK;gBAC9B,MAAM,EAAE,SAAS,CAAC,OAAO;aAC1B,EACD,IAAI,CAAC,gCAAgC,CAAC,GAAG,EAAE,OAAO,CAAC,EACnD,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAClC,CAAA;YAED,MAAM,QAAQ,GAAkC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;gBAClE,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC/B,IAAI,CAAC,IAAI;oBAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACjC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC3B,CAAC,CAAA;YAED,IAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACzD,OAAQ,IAAI,CAAC,OAAqC,CAChD,GAAG,EACH,MAAM,EACN,QAAQ,CACT,CAAA;YACH,CAAC;YAED,OAAQ,IAAI,CAAC,OAA0B,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QAC3D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IACE,GAAG,YAAY,MAAM,CAAC,0BAA0B;gBAChD,GAAG,CAAC,KAAK,KAAK,eAAe,EAC7B,CAAC;gBACD,OAAO,IAAI,CAAC,IAAI,CAAC;oBACf,OAAO,EAAE,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,KAAK;oBAC3C,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;iBAC3C,CAAC,CAAA;YACJ,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IAaD,UAAU,CAAC,GAAoB;QAC7B,OAAO,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAA;IAC9E,CAAC;IAKD,YAAY,CAQV,GAAoB,EACpB,OAAiB;QAEjB,IAAI,CAAE,GAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,KAAK,CACP,sGAAsG,CACvG,CACF,CAAA;QACH,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;QAEvC,IACE,CAAC,GAAG,CAAC,MAAM,KAAK,KAAK,IAAI,UAAU,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,CAAC;YAC5D,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAC1E,CAAC;YACD,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;QAClE,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAC5C,IAAI,EACJ,GAAG,EACH,UAAU,EACV,OAAO,CACR,CAAA;QACH,CAAC;IACH,CAAC;CACF"}