{"name": "openid-client", "version": "6.5.1", "description": "OAuth 2 / OpenID Connect Client API for JavaScript Runtimes", "keywords": ["access token", "auth", "authentication", "authorization", "basic", "browser", "bun", "certified", "ciba", "client", "cloudflare", "deno", "edge", "electron", "fapi", "javascript", "jwt", "netlify", "next", "nextjs", "node", "nodejs", "o<PERSON>h", "oauth2", "oidc", "openid-connect", "openid", "passport", "strategy", "vercel", "workerd", "workers"], "homepage": "https://github.com/panva/openid-client", "repository": "panva/openid-client", "funding": {"url": "https://github.com/sponsors/panva"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./passport": {"types": "./build/passport.d.ts", "default": "./build/passport.js"}, "./package.json": "./package.json"}, "main": "./build/index.js", "types": "./build/index.d.ts", "files": ["build/index.js", "build/index.js.map", "build/index.d.ts", "build/passport.js", "build/passport.js.map", "build/passport.d.ts"], "scripts": {"_format": "find src test examples tap conformance -type f -name '*.ts' -o -name '*.mjs' -o -name '*.cjs' | xargs prettier --check", "build": "rm -rf build && tsc && tsc --removeComments false --declaration --emitDeclarationOnly && tsc -p tsconfig.passport.json && tsc -p tsconfig.passport.json --removeComments false --declaration --emitDeclarationOnly && tsc -p test && npm run --silent check-build && npx --yes jsr publish --dry-run --allow-dirty", "check-build": "tsc --noEmit --types node --lib ES2022.Error && tsc -p conformance && tsc -p tap && tsc -p examples", "conformance": "bash -c 'source .node_flags.sh && ava --config conformance/ava.config.ts'", "docs": "patch-package && typedoc", "docs-check": "./check-examples.sh", "format": "npm run _format -- --write", "format-check": "npm run _format", "tap:browsers": "./tap/.browsers.sh", "tap:bun": "./tap/.bun.sh", "tap:deno": "./tap/.deno.sh", "tap:edge-runtime": "./tap/.edge-runtime.sh", "tap:electron": "./tap/.electron.sh", "tap:node": "bash -c './tap/.node.sh'", "tap:workerd": "./tap/.workerd.sh", "test": "bash -c 'source .node_flags.sh && ava'"}, "dependencies": {"jose": "^6.0.11", "oauth4webapi": "^3.5.2"}, "devDependencies": {"@koa/cors": "^5.0.0", "@types/connect-ensure-login": "^0.1.9", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/express-session": "^1.18.2", "@types/koa__cors": "^5.0.0", "@types/node": "^22.15.30", "@types/passport": "^1.0.17", "@types/qunit": "^2.19.12", "archiver": "^7.0.1", "ava": "^6.4.0", "chrome-launcher": "^1.2.0", "esbuild": "^0.25.5", "ky": "^1.8.1", "oidc-provider": "^9.1.3", "patch-package": "^8.0.0", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "puppeteer-core": "^24.10.0", "qunit": "^2.24.1", "raw-body": "^3.0.0", "selfsigned": "^2.4.1", "tsx": "^4.19.4", "typedoc": "0.27.9", "typedoc-plugin-markdown": "4.4.1", "typedoc-plugin-mdn-links": "4.0.8", "typescript": "^5.8.3", "undici": "^6.21.3"}}