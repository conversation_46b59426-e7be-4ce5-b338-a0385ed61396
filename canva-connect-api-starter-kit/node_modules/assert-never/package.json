{"name": "assert-never", "version": "1.2.1", "description": "Helper function for exhaustive checks of discriminated unions in TypeScript", "main": "index.js", "typings": "index.d.ts", "files": ["index.js", "index.ts", "index.d.ts"], "scripts": {"build": "tsc", "prepublish": "npm run build"}, "keywords": ["typescript", "discriminated unions", "assert", "never"], "repository": "aikoven/assert-never", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"typescript": "^2.2.1"}}