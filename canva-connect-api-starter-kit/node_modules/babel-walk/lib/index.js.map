{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAkC;AAClC,wDAAgC;AAEhC,MAAM,YAAY,GAA+B,CAAS,CAAC,YAAY,CAAC;AACxE,IACE,CAAC,CACC,YAAY;IACZ,mDAAmD;IACnD,OAAO,YAAY,KAAK,QAAQ;IAChC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAC7B,CAAC,GAAG,EAAE,EAAE,CACN,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChC,mDAAmD;QACnD,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CACxD,CACF,EACD;IACA,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;CACH;AAoBD,SAAgB,MAAM,CAAgB,QAAgC;IACpE,MAAM,GAAG,GAAG,iBAAO,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO,CAAC,IAAY,EAAE,KAAa,EAAE,EAAE;QACrC,CAAC,SAAS,OAAO,CAAC,IAAI;YACpB,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE;gBAClB,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE;oBAC7B,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;iBAChB;aACF;YAED,KAAK,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;gBAC/C,MAAM,OAAO,GAAI,IAAY,CAAC,GAAG,CAAC,CAAC;gBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC1B,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE;wBAChC,OAAO,CAAC,UAAU,CAAC,CAAC;qBACrB;iBACF;qBAAM;oBACL,OAAO,CAAC,OAAO,CAAC,CAAC;iBAClB;aACF;YAED,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAE;gBACjB,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE;oBAC5B,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;iBAChB;aACF;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC;AAhCD,wBAgCC;AAiBD,SAAgB,QAAQ,CAAgB,QAAiC;IACvE,MAAM,GAAG,GAAG,iBAAO,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO,CAAC,IAAY,EAAE,KAAa,EAAE,EAAE;QACrC,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,CAAC,SAAS,OAAO,CAAC,IAAI;YACpB,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/B,MAAM,KAAK,GAAG,IAAI,KAAK,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvD,IAAI,KAAK;gBAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE;gBAClB,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE;oBAC7B,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;iBAC3B;aACF;YAED,KAAK,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;gBAC/C,MAAM,OAAO,GAAI,IAAY,CAAC,GAAG,CAAC,CAAC;gBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC1B,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE;wBAChC,OAAO,CAAC,UAAU,CAAC,CAAC;qBACrB;iBACF;qBAAM;oBACL,OAAO,CAAC,OAAO,CAAC,CAAC;iBAClB;aACF;YAED,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAE;gBACjB,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE;oBAC5B,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;iBAC3B;aACF;YAED,IAAI,KAAK;gBAAE,SAAS,CAAC,GAAG,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC;AAvCD,4BAuCC;AAUD,SAAgB,SAAS,CAAgB,QAAmC;IAC1E,MAAM,GAAG,GAAG,iBAAO,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO,CAAC,IAAY,EAAE,KAAa,EAAE,EAAE;QACrC,CAAC,SAAS,OAAO,CAAC,IAAY;YAC5B,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE;gBAClB,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE;oBAC7B,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;iBACzB;aACF;iBAAM;gBACL,KAAK,MAAM,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;oBAC/C,MAAM,OAAO,GAAI,IAAY,CAAC,GAAG,CAAC,CAAC;oBACnC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBAC1B,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE;4BAChC,OAAO,CAAC,UAAU,CAAC,CAAC;yBACrB;qBACF;yBAAM;wBACL,OAAO,CAAC,OAAO,CAAC,CAAC;qBAClB;iBACF;aACF;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACX,CAAC,CAAC;AACJ,CAAC;AAzBD,8BAyBC"}