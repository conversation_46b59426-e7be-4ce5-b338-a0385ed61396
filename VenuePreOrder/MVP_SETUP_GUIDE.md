# 🎵 Venue Preorder MVP - Complete Setup Guide

## 📋 **Project Overview**

This is a **Spotify-integrated venue preorder platform** that allows fans to preorder exclusive merchandise for live music events. Artists can upload their own merch or use AI-generated designs, and fans can choose between home delivery or venue pickup.

### **Tech Stack Detected:**
- **Frontend:** React 18.3.1 + TypeScript + Vite 5.4.14
- **Backend:** Node.js 18+ + Express 4.21.2 + TypeScript
- **Database:** PostgreSQL with Drizzle ORM
- **Authentication:** JWT + bcrypt + Supabase Auth
- **Music Integration:** Spotify Web API
- **Payments:** Stripe
- **Real-time:** Supabase (replacing Firebase)
- **Styling:** TailwindCSS + Radix UI

---

## 🔧 **Required Environment Variables**

Create a `.env` file in the root directory with these variables:

```bash
# =============================================================================
# SPOTIFY API CONFIGURATION (REQUIRED)
# =============================================================================
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_REDIRECT_URI=http://localhost:5000/api/auth/spotify/callback

# =============================================================================
# SUPABASE CONFIGURATION (REQUIRED)
# =============================================================================
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED)
# =============================================================================
DATABASE_URL=postgresql://username:password@host:port/database

# =============================================================================
# STRIPE PAYMENT PROCESSING (REQUIRED)
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key

# =============================================================================
# SECURITY (REQUIRED)
# =============================================================================
JWT_SECRET=your_random_jwt_secret_here
SESSION_SECRET=your_random_session_secret_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=5000
```

---

## 🚀 **Quick Start Instructions**

### **1. Install Dependencies**
```bash
npm install
```

### **2. Set Up Your API Keys**

#### **Spotify API Setup:**
1. Go to https://developer.spotify.com/dashboard/applications
2. Create a new app
3. Add redirect URI: `http://localhost:5000/api/auth/spotify/callback`
4. Copy Client ID and Client Secret to `.env`

#### **Supabase Setup:**
1. Go to https://supabase.com
2. Create a new project
3. Go to Settings → API
4. Copy URL and anon key to `.env`
5. Enable Authentication providers (optional)

#### **Database Setup:**
- **Option A:** Use Supabase's built-in PostgreSQL
- **Option B:** Use Neon (https://neon.tech) for free PostgreSQL
- **Option C:** Use local PostgreSQL

#### **Stripe Setup:**
1. Go to https://stripe.com
2. Create account and get test API keys
3. Add keys to `.env`

### **3. Generate Security Secrets**
```bash
# Generate JWT secret
openssl rand -base64 32

# Generate session secret
openssl rand -base64 32
```

### **4. Set Up Database**
```bash
# Push database schema
npm run db:push
```

### **5. Start Development Server**
```bash
# Start both frontend and backend
npm run dev
```

The app will be available at: **http://localhost:5000**

---

## 🎯 **Key Features Implemented**

### **✅ Spotify Integration**
- OAuth login with Spotify
- Artist search and discovery
- Top tracks and artist data
- Mock event generation
- User profile integration

### **✅ Multi-Role Authentication**
- **Guests:** Order merchandise and food
- **Artists:** Upload merch, track sales
- **Admins:** Manage operations, view analytics

### **✅ Merchandise System**
- Artist merchandise upload
- AI merchandise generation (placeholder)
- Real-time inventory management
- Event-specific merchandise

### **✅ Order Management**
- Combined food and merch orders
- Real-time order tracking
- QR code generation
- Pickup scheduling

### **✅ AI-Powered Features**
- Demand forecasting
- Staff scheduling optimization
- Inventory predictions
- Waste reduction analytics

---

## 📁 **Project Structure**

```
VenuePreOrder/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   │   ├── spotify-login.tsx
│   │   │   └── artist-search.tsx
│   │   ├── pages/          # Application pages
│   │   ├── lib/            # Utilities
│   │   │   ├── spotify.ts  # Spotify client service
│   │   │   └── supabase.ts # Supabase client
│   │   └── hooks/          # Custom React hooks
├── server/                 # Express backend
│   ├── spotify.ts          # Spotify Web API integration
│   ├── ai-services.ts      # AI algorithms
│   ├── routes.ts           # API endpoints
│   ├── auth.ts             # Authentication
│   └── storage.ts          # Database operations
├── shared/                 # Shared types and schemas
├── uploads/                # File upload directory
└── .env                    # Environment variables
```

---

## 🔌 **API Endpoints Added**

### **Spotify Integration:**
- `GET /api/auth/spotify` - Initiate Spotify OAuth
- `GET /api/auth/spotify/callback` - OAuth callback
- `GET /api/spotify/profile` - Get user's Spotify profile
- `GET /api/spotify/search/artists` - Search artists
- `GET /api/spotify/artists/:id` - Get artist details + mock events
- `GET /api/spotify/me/top-artists` - User's top artists

### **Merchandise Management:**
- `POST /api/merch/upload` - Upload artist merchandise
- `POST /api/merch/generate` - Generate AI merchandise (placeholder)
- `GET /api/events/:eventId/merch` - Get event merchandise
- `PATCH /api/merch/:id/availability` - Update merch availability

### **Enhanced Orders:**
- `POST /api/orders/new` - Create order with merch + food items

---

## 🔒 **Security Features**

- **Environment Variables:** All secrets stored in `.env`
- **JWT Authentication:** Secure token-based auth
- **Role-Based Access:** Different permissions for guests/artists/admins
- **Password Hashing:** bcrypt for secure password storage
- **Input Validation:** Zod schemas for data validation
- **CORS Protection:** Configured for local development

---

## 🧪 **Testing the MVP**

### **1. Test Spotify Integration:**
1. Visit http://localhost:5000
2. Click "Connect with Spotify"
3. Authorize the app
4. Search for artists
5. View artist details and mock events

### **2. Test User Roles:**
- **Guest:** Register and place orders
- **Artist:** Upload merchandise
- **Admin:** View AI-powered dashboard

### **3. Test Order Flow:**
1. Browse events and merchandise
2. Add items to cart
3. Complete checkout with Stripe test card: `4242 4242 4242 4242`
4. Track order status

---

## 🚨 **Known Issues & TODOs**

### **Immediate Fixes Needed:**
- [ ] Add missing UI components import paths
- [ ] Configure Tailwind CSS properly
- [ ] Set up file upload directory permissions
- [ ] Add error boundaries for React components

### **Future Enhancements:**
- [ ] Integrate real AI image generation (DALL-E, Midjourney)
- [ ] Add real event data from ticketing APIs
- [ ] Implement push notifications
- [ ] Add mobile app support
- [ ] Integrate with venue POS systems

---

## 🆘 **Troubleshooting**

### **Common Issues:**

**"Module not found" errors:**
```bash
npm install
rm -rf node_modules package-lock.json
npm install
```

**Database connection issues:**
- Verify `DATABASE_URL` is correct
- Check if database server is running
- Ensure database exists and user has permissions

**Spotify authentication fails:**
- Verify `SPOTIFY_CLIENT_ID` and `SPOTIFY_CLIENT_SECRET`
- Check redirect URI matches exactly
- Ensure Spotify app is not in development mode restrictions

**Build errors:**
```bash
npm run check  # TypeScript type checking
npm run build  # Test production build
```

---

## 🎉 **You're Ready!**

Your Spotify-integrated venue preorder MVP is now ready for local development and testing. The platform supports:

- **Artist discovery** through Spotify integration
- **Merchandise management** with upload and AI generation
- **Multi-role authentication** for different user types
- **Real-time order tracking** and management
- **AI-powered operational insights**

Start by connecting your Spotify account and exploring the artist search functionality!
