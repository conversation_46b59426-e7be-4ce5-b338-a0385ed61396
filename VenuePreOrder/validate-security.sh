#!/bin/bash

# =============================================================================
# SECURITY VALIDATION SCRIPT
# =============================================================================
# This script validates that all credentials are properly configured and secure

set -e  # Exit on any error

echo "🔐 Validating Security Configuration..."
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ISSUES=0
WARNINGS=0

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found${NC}"
    ISSUES=$((ISSUES + 1))
else
    echo -e "${GREEN}✅ .env file exists${NC}"
fi

# Check if .env is in .gitignore
if grep -q "^\.env$" .gitignore; then
    echo -e "${GREEN}✅ .env is protected by .gitignore${NC}"
else
    echo -e "${RED}❌ .env is NOT protected by .gitignore${NC}"
    ISSUES=$((ISSUES + 1))
fi

# Source .env file for validation
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
fi

echo ""
echo "🔍 Checking API Credentials..."
echo "=============================="

# Validate Spotify credentials
if [ ! -z "$SPOTIFY_CLIENT_ID" ] && [ "$SPOTIFY_CLIENT_ID" != "your_spotify_client_id_here" ]; then
    echo -e "${GREEN}✅ Spotify Client ID configured${NC}"
else
    echo -e "${RED}❌ Spotify Client ID missing or not configured${NC}"
    ISSUES=$((ISSUES + 1))
fi

if [ ! -z "$SPOTIFY_CLIENT_SECRET" ] && [ "$SPOTIFY_CLIENT_SECRET" != "your_spotify_client_secret_here" ]; then
    echo -e "${GREEN}✅ Spotify Client Secret configured${NC}"
else
    echo -e "${RED}❌ Spotify Client Secret missing or not configured${NC}"
    ISSUES=$((ISSUES + 1))
fi

# Validate Supabase credentials
if [ ! -z "$SUPABASE_URL" ] && [ "$SUPABASE_URL" != "https://your-project.supabase.co" ]; then
    echo -e "${GREEN}✅ Supabase URL configured${NC}"
else
    echo -e "${RED}❌ Supabase URL missing or not configured${NC}"
    ISSUES=$((ISSUES + 1))
fi

if [ ! -z "$SUPABASE_ANON_KEY" ] && [ "$SUPABASE_ANON_KEY" != "your_supabase_anon_key" ]; then
    echo -e "${GREEN}✅ Supabase Anon Key configured${NC}"
else
    echo -e "${RED}❌ Supabase Anon Key missing or not configured${NC}"
    ISSUES=$((ISSUES + 1))
fi

# Validate Printful credentials
if [ ! -z "$PRINTFUL_API_KEY" ] && [ "$PRINTFUL_API_KEY" != "your_printful_api_key_here" ]; then
    echo -e "${GREEN}✅ Printful API Key configured${NC}"
else
    echo -e "${YELLOW}⚠️  Printful API Key missing (optional for basic functionality)${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# Validate Canva credentials
if [ ! -z "$CANVA_CLIENT_ID" ] && [ "$CANVA_CLIENT_ID" != "your_canva_client_id_here" ]; then
    echo -e "${GREEN}✅ Canva Client ID configured${NC}"
else
    echo -e "${YELLOW}⚠️  Canva Client ID missing (optional for design features)${NC}"
    WARNINGS=$((WARNINGS + 1))
fi

# Validate security secrets
if [ ! -z "$JWT_SECRET" ] && [ "$JWT_SECRET" != "your_super_secret_jwt_key_here_make_it_long_and_random" ]; then
    echo -e "${GREEN}✅ JWT Secret configured${NC}"
    
    # Check JWT secret length
    if [ ${#JWT_SECRET} -lt 32 ]; then
        echo -e "${YELLOW}⚠️  JWT Secret is shorter than recommended (32+ characters)${NC}"
        WARNINGS=$((WARNINGS + 1))
    fi
else
    echo -e "${RED}❌ JWT Secret missing or not configured${NC}"
    ISSUES=$((ISSUES + 1))
fi

if [ ! -z "$SESSION_SECRET" ] && [ "$SESSION_SECRET" != "your_session_secret_here_also_make_it_random" ]; then
    echo -e "${GREEN}✅ Session Secret configured${NC}"
    
    # Check session secret length
    if [ ${#SESSION_SECRET} -lt 32 ]; then
        echo -e "${YELLOW}⚠️  Session Secret is shorter than recommended (32+ characters)${NC}"
        WARNINGS=$((WARNINGS + 1))
    fi
else
    echo -e "${RED}❌ Session Secret missing or not configured${NC}"
    ISSUES=$((ISSUES + 1))
fi

echo ""
echo "🔒 Checking Security Best Practices..."
echo "======================================"

# Check for hardcoded credentials in source files
echo "🔍 Scanning for hardcoded credentials..."
HARDCODED_FOUND=0

# Check for obvious hardcoded credentials (simplified check)
if grep -r "client_secret.*=.*['\"][a-zA-Z0-9]\{20,\}['\"]" --include="*.ts" --include="*.js" client/ server/ 2>/dev/null | grep -v "process.env" | grep -v "your_.*_here" >/dev/null; then
    echo -e "${RED}❌ Potential hardcoded client secret found${NC}"
    HARDCODED_FOUND=1
    ISSUES=$((ISSUES + 1))
elif grep -r "api_key.*=.*['\"][a-zA-Z0-9]\{20,\}['\"]" --include="*.ts" --include="*.js" client/ server/ 2>/dev/null | grep -v "process.env" | grep -v "your_.*_here" >/dev/null; then
    echo -e "${RED}❌ Potential hardcoded API key found${NC}"
    HARDCODED_FOUND=1
    ISSUES=$((ISSUES + 1))
fi

if [ $HARDCODED_FOUND -eq 0 ]; then
    echo -e "${GREEN}✅ No hardcoded credentials detected${NC}"
fi

# Check file permissions on .env
if [ -f ".env" ]; then
    ENV_PERMS=$(stat -f "%A" .env 2>/dev/null || stat -c "%a" .env 2>/dev/null)
    if [ "$ENV_PERMS" = "600" ] || [ "$ENV_PERMS" = "644" ]; then
        echo -e "${GREEN}✅ .env file permissions are appropriate${NC}"
    else
        echo -e "${YELLOW}⚠️  .env file permissions could be more restrictive (recommended: 600)${NC}"
        WARNINGS=$((WARNINGS + 1))
    fi
fi

# Check for backup files
if [ -f "CREDENTIALS_BACKUP.md" ]; then
    if grep -q "CREDENTIALS_BACKUP.md" .gitignore; then
        echo -e "${GREEN}✅ Credentials backup file is protected by .gitignore${NC}"
    else
        echo -e "${RED}❌ Credentials backup file is NOT protected by .gitignore${NC}"
        ISSUES=$((ISSUES + 1))
    fi
fi

echo ""
echo "📊 Security Validation Summary"
echo "=============================="

if [ $ISSUES -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    echo -e "${GREEN}🎉 Perfect! All security checks passed${NC}"
    echo -e "${GREEN}✅ Your credentials are properly configured and secure${NC}"
elif [ $ISSUES -eq 0 ]; then
    echo -e "${YELLOW}⚠️  Security validation passed with $WARNINGS warning(s)${NC}"
    echo -e "${GREEN}✅ All critical security requirements are met${NC}"
else
    echo -e "${RED}❌ Security validation failed with $ISSUES critical issue(s) and $WARNINGS warning(s)${NC}"
    echo -e "${RED}🚨 Please fix the critical issues before proceeding${NC}"
fi

echo ""
echo "🛡️ Security Recommendations:"
echo "============================"
echo "1. Never commit .env files to version control"
echo "2. Use different credentials for dev/staging/production"
echo "3. Rotate API keys regularly (quarterly recommended)"
echo "4. Enable 2FA on all service accounts"
echo "5. Monitor API usage for anomalies"
echo "6. Keep backup codes in a secure location"

echo ""
if [ $ISSUES -gt 0 ]; then
    echo -e "${RED}🚨 Fix critical issues before deploying to production!${NC}"
    exit 1
else
    echo -e "${GREEN}🚀 Security validation complete - ready for development!${NC}"
    exit 0
fi
