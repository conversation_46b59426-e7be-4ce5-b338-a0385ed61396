# 🚀 Venue Preorder Platform - Deployment Setup Guide

## 📋 Overview
This is a complete venue preorder platform built with React, Node.js, PostgreSQL, and Stripe. It supports food & merchandise pre-orders, real-time order tracking, and multi-role access (guests, artists, admins).

## 🛠 Prerequisites
- **Node.js 18+** - JavaScript runtime
- **PostgreSQL 12+** - Database
- **Stripe Account** - Payment processing
- **Firebase Project** (optional) - Real-time features

## 🔧 Required Environment Variables

### 1. Database Configuration (REQUIRED)
```bash
# Primary database connection
DATABASE_URL=postgresql://username:password@host:port/database

# Or individual PostgreSQL settings
PGHOST=your-postgres-host
PGPORT=5432
PGUSER=your-username
PGPASSWORD=your-password
PGDATABASE=your-database-name
```

### 2. Stripe Payment Processing (REQUIRED)
```bash
# Get these from your Stripe Dashboard
VITE_STRIPE_PUBLIC_KEY=pk_test_... # or pk_live_...
STRIPE_SECRET_KEY=sk_test_...      # or sk_live_...
```

### 3. Authentication & Security (REQUIRED)
```bash
# Generate random strings for these
JWT_SECRET=your_super_secret_jwt_key_here
SESSION_SECRET=your_session_secret_here
```

### 4. Firebase (OPTIONAL - for real-time features)
```bash
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_APP_ID=your_firebase_app_id
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Set Up Environment Variables
```bash
# Copy the example environment file
cp .env .env.local

# Edit .env.local with your actual values
# See the .env file for all required variables
```

### 3. Set Up Database
```bash
# Push database schema to your PostgreSQL database
npm run db:push
```

### 4. Start Development Server
```bash
# Start both frontend and backend
npm run dev
```

The application will be available at `http://localhost:5000`

## 🌐 Deployment Options

### Option 1: Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Option 2: Railway
1. Connect repository to Railway
2. Add PostgreSQL database service
3. Configure environment variables
4. Deploy

### Option 3: Render
1. Create new web service on Render
2. Add PostgreSQL database
3. Set environment variables
4. Deploy

### Option 4: Replit (Original Platform)
1. Import project to Replit
2. Configure secrets in Replit
3. Run with `npm run dev`

## 📊 Database Setup

The application uses Drizzle ORM with PostgreSQL. Required tables:
- `users` - User accounts and authentication
- `venues` - Venue information
- `events` - Events at venues
- `menu_categories` - Food/merchandise categories
- `menu_items` - Individual items for sale
- `orders` - Customer orders
- `order_items` - Items within orders
- `artist_merch` - Artist merchandise
- `queue_status` - Real-time order queue status

### Database Providers
- **Neon** (recommended) - Serverless PostgreSQL
- **AWS RDS** - Managed PostgreSQL
- **Google Cloud SQL** - Managed PostgreSQL
- **Azure Database** - Managed PostgreSQL
- **Self-hosted** - Your own PostgreSQL instance

## 💳 Stripe Setup

1. Create a Stripe account at https://stripe.com
2. Get your API keys from the Stripe Dashboard
3. For testing: Use test keys (pk_test_... and sk_test_...)
4. For production: Use live keys (pk_live_... and sk_live_...)
5. Set up webhooks for payment confirmations (optional)

## 🔥 Firebase Setup (Optional)

Firebase provides real-time features for order tracking:

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Enable Authentication (optional)
4. Get your config values from Project Settings
5. Add the config to your environment variables

## 🔒 Security Notes

- Never commit `.env` files to version control
- Use strong, random secrets for JWT_SECRET and SESSION_SECRET
- Use HTTPS in production
- Keep Stripe secret keys secure
- Regularly rotate secrets

## 📁 Project Structure

```
VenuePreOrder/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/          # Application pages
│   │   ├── hooks/          # Custom React hooks
│   │   └── lib/            # Utilities and configurations
├── server/                 # Express.js backend
│   ├── auth.ts            # Authentication logic
│   ├── db.ts              # Database connection
│   ├── routes.ts          # API routes
│   └── storage.ts         # Database operations
├── shared/                 # Shared types and schemas
└── uploads/               # File upload directory
```

## 🎯 Features

- **Multi-role access**: Guests, Artists, Admins
- **Pre-event ordering**: Order before arriving at venue
- **Real-time tracking**: Live order status updates
- **Payment processing**: Secure Stripe integration
- **Inventory management**: Track available items
- **Order queue**: Manage order preparation
- **File uploads**: Support for item images
- **Responsive design**: Works on all devices

## 🐛 Troubleshooting

### Database Connection Issues
- Verify DATABASE_URL is correct
- Check if database server is running
- Ensure database exists and user has permissions

### Stripe Payment Issues
- Verify API keys are correct
- Check if keys match environment (test vs live)
- Ensure webhook endpoints are configured

### Build Issues
- Run `npm install` to ensure all dependencies are installed
- Check Node.js version (requires 18+)
- Verify TypeScript compilation with `npm run check`

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the application logs
3. Verify all environment variables are set correctly
4. Ensure external services (database, Stripe) are properly configured
