import type { Express } from "express";
import express from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import Strip<PERSON> from "stripe";
import multer from "multer";
import path from "path";
import { storage } from "./storage";
import { insertOrderSchema, insertOrderItemSchema, insertUserSchema } from "@shared/schema";
import { authenticate, authorize, hashPassword, verifyPassword, generateToken, AuthenticatedRequest } from "./auth";
import * as spotifyService from "./spotify";
import * as printfulService from "./printful";
import * as canvaService from "./canva";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing required Stripe secret: STRIPE_SECRET_KEY');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
});

// Configure multer for image uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: './uploads/',
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
  }),
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication Routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const { email, password, role, firstName, lastName, username } = req.body;
      
      // Check if user already exists
      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      // Hash password
      const passwordHash = await hashPassword(password);
      
      // Create user
      const user = await storage.createUser({
        email,
        passwordHash,
        role: role || 'guest',
        firstName,
        lastName,
        username,
        isGuest: false
      });

      // Generate token
      const token = generateToken({ id: user.id, email: user.email, role: user.role || 'guest' });
      
      res.status(201).json({
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      // Find user
      const user = await storage.getUserByEmail(email);
      if (!user || !user.passwordHash) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Verify password
      const isValid = await verifyPassword(password, user.passwordHash);
      if (!isValid) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Generate token
      const token = generateToken({ id: user.id, email: user.email, role: user.role || 'guest' });
      
      res.json({
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/auth/me", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const user = await storage.getUser(req.user!.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      res.json({
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // =============================================================================
  // SPOTIFY AUTHENTICATION & API ROUTES
  // =============================================================================

  // Spotify OAuth login initiation
  app.get("/api/auth/spotify", (req, res) => {
    try {
      const state = Math.random().toString(36).substring(2, 15);
      // Store state in session for security (in production, use proper session storage)
      const authUrl = spotifyService.getSpotifyAuthUrl(state);
      res.json({ authUrl, state });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Spotify OAuth callback
  app.get("/api/auth/spotify/callback", async (req, res) => {
    try {
      const { code, state, error } = req.query;

      if (error) {
        return res.status(400).json({ message: `Spotify auth error: ${error}` });
      }

      if (!code) {
        return res.status(400).json({ message: "No authorization code received" });
      }

      // Exchange code for tokens
      const tokens = await spotifyService.exchangeCodeForTokens(code as string);

      // Get user profile from Spotify
      const profile = await spotifyService.getUserProfile(tokens.accessToken);

      // Check if user exists in our database
      let user = await storage.getUserByEmail(profile.email || `${profile.id}@spotify.local`);

      if (!user) {
        // Create new user from Spotify profile
        user = await storage.createUser({
          email: profile.email || `${profile.id}@spotify.local`,
          passwordHash: '', // No password for Spotify users
          role: 'guest',
          firstName: profile.displayName?.split(' ')[0] || 'Spotify',
          lastName: profile.displayName?.split(' ').slice(1).join(' ') || 'User',
          username: profile.id,
          isGuest: false,
          spotifyId: profile.id,
          spotifyAccessToken: tokens.accessToken,
          spotifyRefreshToken: tokens.refreshToken
        });
      } else {
        // Update existing user with Spotify tokens
        await storage.updateUser(user.id, {
          spotifyId: profile.id,
          spotifyAccessToken: tokens.accessToken,
          spotifyRefreshToken: tokens.refreshToken
        });
      }

      // Generate our JWT token
      const jwtToken = generateToken({
        id: user.id,
        email: user.email,
        role: user.role || 'guest'
      });

      // Redirect to frontend with token (in production, use secure cookies)
      res.redirect(`http://localhost:5000/?token=${jwtToken}&spotify=true`);
    } catch (error: any) {
      console.error('Spotify callback error:', error);
      res.status(500).json({ message: error.message });
    }
  });

  // Get user's Spotify profile
  app.get("/api/spotify/profile", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const user = await storage.getUser(req.user!.id);
      if (!user?.spotifyAccessToken) {
        return res.status(400).json({ message: "User not connected to Spotify" });
      }

      const profile = await spotifyService.getUserProfile(user.spotifyAccessToken);
      res.json(profile);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Search artists on Spotify
  app.get("/api/spotify/search/artists", async (req, res) => {
    try {
      const { q } = req.query;
      if (!q) {
        return res.status(400).json({ message: "Query parameter 'q' is required" });
      }

      // Use client credentials for public search
      const { accessToken } = await spotifyService.getClientCredentialsToken();
      const artists = await spotifyService.searchArtists(q as string, accessToken);

      res.json(artists);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get artist details and generate mock events
  app.get("/api/spotify/artists/:id", async (req, res) => {
    try {
      const { id } = req.params;

      // Use client credentials for public data
      const { accessToken } = await spotifyService.getClientCredentialsToken();
      const artist = await spotifyService.getArtist(id, accessToken);
      const topTracks = await spotifyService.getArtistTopTracks(id, accessToken);

      // Generate mock event data
      const mockEvents = spotifyService.generateMockEventData(artist);

      res.json({
        artist,
        topTracks,
        events: mockEvents
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get user's top artists (requires Spotify auth)
  app.get("/api/spotify/me/top-artists", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const user = await storage.getUser(req.user!.id);
      if (!user?.spotifyAccessToken) {
        return res.status(400).json({ message: "User not connected to Spotify" });
      }

      const topArtists = await spotifyService.getUserTopArtists(user.spotifyAccessToken);
      res.json(topArtists);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Image upload endpoint for artists
  app.post("/api/upload", authenticate, authorize('artist', 'admin'), upload.single('image'), (req: AuthenticatedRequest, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const imageUrl = `/uploads/${req.file.filename}`;
      res.json({ imageUrl });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Serve uploaded files
  const staticMiddleware = express.static('uploads');
  app.use('/uploads', staticMiddleware);

  // API Routes

  // Venues
  app.get("/api/venues", async (req, res) => {
    try {
      const venues = await storage.getActiveVenues();
      res.json(venues);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/venues/:id", async (req, res) => {
    try {
      const venue = await storage.getVenue(parseInt(req.params.id));
      if (!venue) {
        return res.status(404).json({ message: "Venue not found" });
      }
      res.json(venue);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Events
  app.get("/api/events", async (req, res) => {
    try {
      const events = await storage.getActiveEvents();
      res.json(events);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/events/:id", async (req, res) => {
    try {
      const event = await storage.getEvent(parseInt(req.params.id));
      if (!event) {
        return res.status(404).json({ message: "Event not found" });
      }
      res.json(event);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Menu
  app.get("/api/events/:eventId/menu", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const categories = await storage.getMenuCategories(eventId);
      
      const menuWithItems = await Promise.all(
        categories.map(async (category) => {
          const items = await storage.getMenuItems(category.id);
          return { ...category, items };
        })
      );

      res.json(menuWithItems);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/menu-items/:id", async (req, res) => {
    try {
      const item = await storage.getMenuItem(parseInt(req.params.id));
      if (!item) {
        return res.status(404).json({ message: "Menu item not found" });
      }
      res.json(item);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Users
  app.post("/api/users", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const user = await storage.createUser(userData);
      res.status(201).json(user);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/users/:id", async (req, res) => {
    try {
      const user = await storage.getUser(parseInt(req.params.id));
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Orders
  app.post("/api/orders", async (req, res) => {
    try {
      const { items, ...orderData } = req.body;
      
      // Validate order data
      const validatedOrder = insertOrderSchema.parse(orderData);
      
      // Create order
      const order = await storage.createOrder(validatedOrder);
      
      // Create order items
      if (items && Array.isArray(items)) {
        for (const item of items) {
          const orderItem = insertOrderItemSchema.parse({
            ...item,
            orderId: order.id
          });
          await storage.createOrderItem(orderItem);
          
          // Update menu item stock
          const menuItem = await storage.getMenuItem(item.menuItemId);
          if (menuItem) {
            await storage.updateMenuItemStock(
              item.menuItemId, 
              Math.max(0, menuItem.stock - item.quantity)
            );
          }
        }
      }

      // Update queue status
      const queueStatus = await storage.getQueueStatus(order.eventId);
      if (queueStatus) {
        await storage.updateQueueStatus(order.eventId, {
          preparing: queueStatus.preparing + 1,
          ready: queueStatus.ready,
          avgWaitTime: queueStatus.avgWaitTime
        });
      }

      res.status(201).json(order);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/orders/:id", async (req, res) => {
    try {
      const orderId = parseInt(req.params.id);
      const order = await storage.getOrder(orderId);
      
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      const items = await storage.getOrderItems(orderId);
      
      // Get menu item details for each order item
      const itemsWithDetails = await Promise.all(
        items.map(async (item) => {
          const menuItem = await storage.getMenuItem(item.menuItemId);
          return { ...item, menuItem };
        })
      );

      res.json({ ...order, items: itemsWithDetails });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.patch("/api/orders/:id/status", async (req, res) => {
    try {
      const { status } = req.body;
      const orderId = parseInt(req.params.id);
      
      const order = await storage.updateOrderStatus(orderId, status);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Update queue status based on status change
      const queueStatus = await storage.getQueueStatus(order.eventId);
      if (queueStatus) {
        let updates = { ...queueStatus };
        
        if (status === 'ready') {
          updates.preparing = Math.max(0, updates.preparing - 1);
          updates.ready = updates.ready + 1;
        } else if (status === 'completed') {
          updates.ready = Math.max(0, updates.ready - 1);
        }
        
        await storage.updateQueueStatus(order.eventId, updates);
      }

      // Broadcast update via WebSocket (temporarily disabled)
      // wss.clients.forEach((client) => {
      //   if (client.readyState === WebSocket.OPEN) {
      //     client.send(JSON.stringify({
      //       type: 'orderStatusUpdate',
      //       orderId: order.id,
      //       status: order.status,
      //       eventId: order.eventId
      //     }));
      //   }
      // });

      res.json(order);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/events/:eventId/orders", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const orders = await storage.getOrdersByEvent(eventId);
      
      const ordersWithItems = await Promise.all(
        orders.map(async (order) => {
          const items = await storage.getOrderItems(order.id);
          return { ...order, items };
        })
      );

      res.json(ordersWithItems);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Queue Status
  app.get("/api/events/:eventId/queue-status", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const status = await storage.getQueueStatus(eventId);
      
      if (!status) {
        return res.status(404).json({ message: "Queue status not found" });
      }

      res.json(status);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Payment Integration
  app.post("/api/create-payment-intent", async (req, res) => {
    try {
      const { amount, eventId, userId } = req.body;
      
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: "usd",
        metadata: {
          eventId: eventId.toString(),
          userId: userId?.toString() || 'guest'
        }
      });

      res.json({ clientSecret: paymentIntent.client_secret });
    } catch (error: any) {
      res.status(500).json({ message: "Error creating payment intent: " + error.message });
    }
  });

  // =============================================================================
  // MERCHANDISE MANAGEMENT ROUTES
  // =============================================================================

  // Upload new merchandise (for artists)
  app.post("/api/merch/upload", authenticate, authorize('artist', 'admin'), upload.single('image'), async (req: AuthenticatedRequest, res) => {
    try {
      const { name, description, price, stock, eventId, category, sizes, isPresaleOnly, isVipExclusive } = req.body;

      if (!req.file) {
        return res.status(400).json({ message: 'Image file is required' });
      }

      const imageUrl = `/uploads/${req.file.filename}`;

      // Create merchandise item
      const merch = await storage.createArtistMerch({
        artistId: req.user!.id,
        eventId: parseInt(eventId),
        name,
        description,
        price: parseFloat(price),
        stock: parseInt(stock),
        imageUrl,
        category: category || 'apparel',
        sizes: sizes ? JSON.parse(sizes) : null,
        isPresaleOnly: isPresaleOnly === 'true',
        isVipExclusive: isVipExclusive === 'true',
        isAvailable: true
      });

      res.status(201).json(merch);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Generate AI-powered merchandise (placeholder for future AI integration)
  app.post("/api/merch/generate", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { artistId, eventId, style, colors, merchType } = req.body;

      // TODO: Integrate with AI image generation service (DALL-E, Midjourney, etc.)
      // For now, return a placeholder response

      const generatedMerch = {
        id: Date.now(),
        artistId: artistId || req.user!.id,
        eventId: parseInt(eventId),
        name: `AI Generated ${merchType || 'T-Shirt'}`,
        description: `Custom ${merchType || 'T-Shirt'} design generated with AI for this event`,
        price: 25.00,
        stock: 100,
        imageUrl: '/uploads/ai-generated-placeholder.jpg', // Placeholder
        category: 'apparel',
        sizes: ['S', 'M', 'L', 'XL'],
        isPresaleOnly: false,
        isVipExclusive: false,
        isAvailable: true,
        aiGenerated: true,
        generationParams: {
          style,
          colors,
          merchType,
          prompt: `${style} style ${merchType} with ${colors} colors`
        }
      };

      // TODO: Actually create the merch item in database after AI generation
      // const merch = await storage.createArtistMerch(generatedMerch);

      res.status(201).json({
        message: "AI merchandise generation initiated",
        merch: generatedMerch,
        status: "pending_generation",
        estimatedTime: "2-5 minutes"
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Create new order (enhanced with merch support)
  app.post("/api/orders/new", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const { eventId, items, deliveryMethod, pickupLocation, specialInstructions } = req.body;

      // Calculate total amount
      let totalAmount = 0;
      const orderItems = [];

      for (const item of items) {
        let itemPrice = 0;
        let itemName = '';

        if (item.type === 'menu') {
          const menuItem = await storage.getMenuItem(item.id);
          if (!menuItem) {
            return res.status(400).json({ message: `Menu item ${item.id} not found` });
          }
          itemPrice = parseFloat(menuItem.price);
          itemName = menuItem.name;
        } else if (item.type === 'merch') {
          const merchItem = await storage.getArtistMerchItem(item.id);
          if (!merchItem) {
            return res.status(400).json({ message: `Merch item ${item.id} not found` });
          }
          itemPrice = parseFloat(merchItem.price);
          itemName = merchItem.name;
        }

        const itemTotal = itemPrice * item.quantity;
        totalAmount += itemTotal;

        orderItems.push({
          itemId: item.id,
          itemType: item.type,
          itemName,
          quantity: item.quantity,
          unitPrice: itemPrice,
          totalPrice: itemTotal,
          customizations: item.customizations || {}
        });
      }

      // Create order
      const order = await storage.createOrder({
        userId: req.user!.id,
        eventId: parseInt(eventId),
        totalAmount,
        status: 'pending',
        deliveryMethod: deliveryMethod || 'pickup',
        pickupLocation,
        specialInstructions,
        qrCode: `ORDER_${Date.now()}_${req.user!.id}`,
        items: orderItems
      });

      res.status(201).json(order);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get artist merchandise
  app.get("/api/artists/:artistId/events/:eventId/merch", async (req, res) => {
    try {
      const artistId = parseInt(req.params.artistId);
      const eventId = parseInt(req.params.eventId);
      const merch = await storage.getArtistMerch(artistId, eventId);
      res.json(merch);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get all merchandise for an event
  app.get("/api/events/:eventId/merch", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const merch = await storage.getEventMerch(eventId);
      res.json(merch);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Update merchandise availability (for artists)
  app.patch("/api/merch/:id/availability", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const merchId = parseInt(req.params.id);
      const { isAvailable, stock } = req.body;

      const merch = await storage.updateMerchAvailability(merchId, isAvailable, stock);
      if (!merch) {
        return res.status(404).json({ message: "Merchandise not found" });
      }

      res.json(merch);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // =============================================================================
  // PRINTFUL INTEGRATION ROUTES
  // =============================================================================

  // Get available Printful products
  app.get("/api/printful/products", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const products = await printfulService.getAvailableProducts();
      res.json(products);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get Printful product details
  app.get("/api/printful/products/:id", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const productId = parseInt(req.params.id);
      const productDetails = await printfulService.getProductDetails(productId);
      res.json(productDetails);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Create Printful store product with design
  app.post("/api/printful/store-products", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { name, description, variantId, designFileId, retailPrice, eventId } = req.body;

      const storeProduct = await printfulService.createStoreProduct({
        name,
        description,
        variantId: parseInt(variantId),
        designFileId: parseInt(designFileId),
        retailPrice: parseFloat(retailPrice),
        artistId: req.user!.id.toString(),
        eventId: eventId.toString()
      });

      res.status(201).json(storeProduct);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Upload design file to Printful
  app.post("/api/printful/upload-design", authenticate, authorize('artist', 'admin'), upload.single('design'), async (req: AuthenticatedRequest, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'Design file is required' });
      }

      const fileInfo = await printfulService.uploadDesignFile(req.file.buffer, req.file.originalname);
      res.status(201).json(fileInfo);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Calculate shipping costs
  app.post("/api/printful/shipping", async (req, res) => {
    try {
      const { recipient, items } = req.body;
      const shippingRates = await printfulService.calculateShipping({ recipient, items });
      res.json(shippingRates);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Create Printful order for fulfillment
  app.post("/api/printful/orders", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const orderData = req.body;
      const printfulOrder = await printfulService.createPrintfulOrder(orderData);
      res.status(201).json(printfulOrder);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get Printful order status
  app.get("/api/printful/orders/:id", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const orderId = parseInt(req.params.id);
      const orderStatus = await printfulService.getOrderStatus(orderId);
      res.json(orderStatus);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Printful webhook endpoint
  app.post("/api/printful/webhook", async (req, res) => {
    try {
      const webhookData = req.body;
      const processedData = printfulService.processWebhook(webhookData);

      // TODO: Update order status in database based on webhook
      console.log('Printful webhook received:', processedData);

      res.status(200).json({ message: 'Webhook processed successfully' });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // =============================================================================
  // CANVA INTEGRATION ROUTES
  // =============================================================================

  // Initiate Canva OAuth authentication
  app.get("/api/canva/auth", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { artistId } = req.query;
      const authData = canvaService.generateCanvaAuthUrl(
        req.user!.id.toString(),
        artistId as string || req.user!.id.toString()
      );
      res.json(authData);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Canva OAuth callback
  app.get("/api/canva/oauth/redirect", async (req, res) => {
    try {
      const { code, state, error } = req.query;

      if (error) {
        return res.status(400).json({ message: `Canva auth error: ${error}` });
      }

      if (!code || !state) {
        return res.status(400).json({ message: "Missing authorization code or state" });
      }

      // Exchange code for tokens
      const authResult = await canvaService.exchangeCanvaCode(code as string, state as string);

      // TODO: Store Canva tokens in database for the user
      // For now, we'll redirect back to the frontend with success

      res.redirect(`${process.env.CANVA_BASE_URL}/?canva_auth=success&user_id=${authResult.userId}`);
    } catch (error: any) {
      console.error('Canva OAuth callback error:', error);
      res.redirect(`${process.env.CANVA_BASE_URL}/?canva_auth=error&message=${encodeURIComponent(error.message)}`);
    }
  });

  // Canva return navigation
  app.get("/api/canva/return-nav", async (req, res) => {
    try {
      // This endpoint is called when users navigate back from Canva
      res.redirect(`${process.env.CANVA_BASE_URL}/dashboard?tab=designs`);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Create new design in Canva
  app.post("/api/canva/designs", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { designType, title, accessToken } = req.body;

      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }

      const design = await canvaService.createCanvaDesign(accessToken, designType, title);
      res.status(201).json(design);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get user's Canva designs
  app.get("/api/canva/designs", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { accessToken, limit } = req.query;

      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }

      const designs = await canvaService.getUserDesigns(accessToken as string, parseInt(limit as string) || 20);
      res.json(designs);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Export design from Canva
  app.post("/api/canva/designs/:id/export", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { id } = req.params;
      const { accessToken, format } = req.body;

      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }

      const exportJob = await canvaService.exportCanvaDesign(accessToken, id, format || 'PNG');
      res.status(201).json(exportJob);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get export job status
  app.get("/api/canva/exports/:jobId", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { jobId } = req.params;
      const { accessToken } = req.query;

      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }

      const jobStatus = await canvaService.getExportJobStatus(accessToken as string, jobId);
      res.json(jobStatus);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Process design export from Canva
  app.post("/api/canva/export", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { exportData, artistId, eventId } = req.body;

      const processedDesign = await canvaService.processCanvaExport(
        exportData,
        artistId || req.user!.id.toString(),
        eventId
      );

      // TODO: Save design to database and upload to Printful
      res.status(201).json(processedDesign);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Get merchandise templates
  app.get("/api/canva/templates/:merchType", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { merchType } = req.params;
      const template = canvaService.createMerchTemplate(merchType);
      res.json(template);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Generate artist brand configuration
  app.post("/api/canva/artist-brand", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { artistData } = req.body;
      const brandConfig = canvaService.generateArtistBrand(artistData);
      res.json(brandConfig);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Validate design for print
  app.post("/api/canva/validate-design", authenticate, authorize('artist', 'admin'), async (req: AuthenticatedRequest, res) => {
    try {
      const { designData } = req.body;
      const validation = canvaService.validateDesignForPrint(designData);
      res.json(validation);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Canva webhook endpoint
  app.post("/api/canva/webhook", async (req, res) => {
    try {
      const webhookData = req.body;
      const processedData = canvaService.processCanvaWebhook(webhookData);

      // TODO: Handle design events (created, updated, exported)
      console.log('Canva webhook received:', processedData);

      res.status(200).json({ message: 'Webhook processed successfully' });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  const httpServer = createServer(app);

  // WebSocket Server for real-time updates (temporarily disabled)
  // const wss = new WebSocketServer({
  //   server: httpServer,
  //   path: '/ws'
  // });

  // wss.on('connection', (ws) => {
  //   console.log('Client connected to WebSocket');

  //   ws.on('message', (message) => {
  //     try {
  //       const data = JSON.parse(message.toString());

  //       // Handle different message types
  //       if (data.type === 'subscribe') {
  //         // Subscribe client to specific event updates
  //         ws.eventId = data.eventId;
  //       }
  //     } catch (error) {
  //       console.error('WebSocket message error:', error);
  //     }
  //   });

  //   ws.on('close', () => {
  //     console.log('Client disconnected from WebSocket');
  //   });

  //   // Send initial connection confirmation
  //   ws.send(JSON.stringify({ type: 'connected' }));
  // });

  // Simulate real-time queue updates every 10 seconds
  setInterval(async () => {
    try {
      const events = await storage.getActiveEvents();
      
      for (const event of events) {
        const currentStatus = await storage.getQueueStatus(event.id);
        if (currentStatus) {
          // Simulate queue changes
          const preparing = Math.max(0, currentStatus.preparing + Math.floor(Math.random() * 3) - 1);
          const ready = Math.max(0, currentStatus.ready + Math.floor(Math.random() * 2) - 1);
          const avgWaitTime = Math.max(5, Math.min(20, currentStatus.avgWaitTime + Math.floor(Math.random() * 6) - 3));

          await storage.updateQueueStatus(event.id, {
            preparing,
            ready,
            avgWaitTime
          });

          // Broadcast to all connected clients (temporarily disabled)
          // wss.clients.forEach((client) => {
          //   if (client.readyState === WebSocket.OPEN) {
          //     client.send(JSON.stringify({
          //       type: 'queueUpdate',
          //       eventId: event.id,
          //       preparing,
          //       ready,
          //       avgWaitTime
          //     }));
          //   }
          // });
        }
      }
    } catch (error) {
      console.error('Queue update error:', error);
    }
  }, 10000);

  return httpServer;
}
