import type { Express } from "express";
import express from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import <PERSON><PERSON> from "stripe";
import multer from "multer";
import path from "path";
import { storage } from "./storage";
import { insertOrderSchema, insertOrderItemSchema, insertUserSchema } from "@shared/schema";
import { authenticate, authorize, hashPassword, verifyPassword, generateToken, AuthenticatedRequest } from "./auth";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing required Stripe secret: STRIPE_SECRET_KEY');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
});

// Configure multer for image uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: './uploads/',
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
  }),
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication Routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const { email, password, role, firstName, lastName, username } = req.body;
      
      // Check if user already exists
      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      // Hash password
      const passwordHash = await hashPassword(password);
      
      // Create user
      const user = await storage.createUser({
        email,
        passwordHash,
        role: role || 'guest',
        firstName,
        lastName,
        username,
        isGuest: false
      });

      // Generate token
      const token = generateToken({ id: user.id, email: user.email, role: user.role || 'guest' });
      
      res.status(201).json({
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      // Find user
      const user = await storage.getUserByEmail(email);
      if (!user || !user.passwordHash) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Verify password
      const isValid = await verifyPassword(password, user.passwordHash);
      if (!isValid) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Generate token
      const token = generateToken({ id: user.id, email: user.email, role: user.role || 'guest' });
      
      res.json({
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/auth/me", authenticate, async (req: AuthenticatedRequest, res) => {
    try {
      const user = await storage.getUser(req.user!.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      res.json({
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Image upload endpoint for artists
  app.post("/api/upload", authenticate, authorize('artist', 'admin'), upload.single('image'), (req: AuthenticatedRequest, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const imageUrl = `/uploads/${req.file.filename}`;
      res.json({ imageUrl });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Serve uploaded files
  const staticMiddleware = express.static('uploads');
  app.use('/uploads', staticMiddleware);

  // API Routes

  // Venues
  app.get("/api/venues", async (req, res) => {
    try {
      const venues = await storage.getActiveVenues();
      res.json(venues);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/venues/:id", async (req, res) => {
    try {
      const venue = await storage.getVenue(parseInt(req.params.id));
      if (!venue) {
        return res.status(404).json({ message: "Venue not found" });
      }
      res.json(venue);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Events
  app.get("/api/events", async (req, res) => {
    try {
      const events = await storage.getActiveEvents();
      res.json(events);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/events/:id", async (req, res) => {
    try {
      const event = await storage.getEvent(parseInt(req.params.id));
      if (!event) {
        return res.status(404).json({ message: "Event not found" });
      }
      res.json(event);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Menu
  app.get("/api/events/:eventId/menu", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const categories = await storage.getMenuCategories(eventId);
      
      const menuWithItems = await Promise.all(
        categories.map(async (category) => {
          const items = await storage.getMenuItems(category.id);
          return { ...category, items };
        })
      );

      res.json(menuWithItems);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/menu-items/:id", async (req, res) => {
    try {
      const item = await storage.getMenuItem(parseInt(req.params.id));
      if (!item) {
        return res.status(404).json({ message: "Menu item not found" });
      }
      res.json(item);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Users
  app.post("/api/users", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const user = await storage.createUser(userData);
      res.status(201).json(user);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/users/:id", async (req, res) => {
    try {
      const user = await storage.getUser(parseInt(req.params.id));
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Orders
  app.post("/api/orders", async (req, res) => {
    try {
      const { items, ...orderData } = req.body;
      
      // Validate order data
      const validatedOrder = insertOrderSchema.parse(orderData);
      
      // Create order
      const order = await storage.createOrder(validatedOrder);
      
      // Create order items
      if (items && Array.isArray(items)) {
        for (const item of items) {
          const orderItem = insertOrderItemSchema.parse({
            ...item,
            orderId: order.id
          });
          await storage.createOrderItem(orderItem);
          
          // Update menu item stock
          const menuItem = await storage.getMenuItem(item.menuItemId);
          if (menuItem) {
            await storage.updateMenuItemStock(
              item.menuItemId, 
              Math.max(0, menuItem.stock - item.quantity)
            );
          }
        }
      }

      // Update queue status
      const queueStatus = await storage.getQueueStatus(order.eventId);
      if (queueStatus) {
        await storage.updateQueueStatus(order.eventId, {
          preparing: queueStatus.preparing + 1,
          ready: queueStatus.ready,
          avgWaitTime: queueStatus.avgWaitTime
        });
      }

      res.status(201).json(order);
    } catch (error: any) {
      res.status(400).json({ message: error.message });
    }
  });

  app.get("/api/orders/:id", async (req, res) => {
    try {
      const orderId = parseInt(req.params.id);
      const order = await storage.getOrder(orderId);
      
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      const items = await storage.getOrderItems(orderId);
      
      // Get menu item details for each order item
      const itemsWithDetails = await Promise.all(
        items.map(async (item) => {
          const menuItem = await storage.getMenuItem(item.menuItemId);
          return { ...item, menuItem };
        })
      );

      res.json({ ...order, items: itemsWithDetails });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.patch("/api/orders/:id/status", async (req, res) => {
    try {
      const { status } = req.body;
      const orderId = parseInt(req.params.id);
      
      const order = await storage.updateOrderStatus(orderId, status);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Update queue status based on status change
      const queueStatus = await storage.getQueueStatus(order.eventId);
      if (queueStatus) {
        let updates = { ...queueStatus };
        
        if (status === 'ready') {
          updates.preparing = Math.max(0, updates.preparing - 1);
          updates.ready = updates.ready + 1;
        } else if (status === 'completed') {
          updates.ready = Math.max(0, updates.ready - 1);
        }
        
        await storage.updateQueueStatus(order.eventId, updates);
      }

      // Broadcast update via WebSocket
      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify({
            type: 'orderStatusUpdate',
            orderId: order.id,
            status: order.status,
            eventId: order.eventId
          }));
        }
      });

      res.json(order);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  app.get("/api/events/:eventId/orders", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const orders = await storage.getOrdersByEvent(eventId);
      
      const ordersWithItems = await Promise.all(
        orders.map(async (order) => {
          const items = await storage.getOrderItems(order.id);
          return { ...order, items };
        })
      );

      res.json(ordersWithItems);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Queue Status
  app.get("/api/events/:eventId/queue-status", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const status = await storage.getQueueStatus(eventId);
      
      if (!status) {
        return res.status(404).json({ message: "Queue status not found" });
      }

      res.json(status);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  // Payment Integration
  app.post("/api/create-payment-intent", async (req, res) => {
    try {
      const { amount, eventId, userId } = req.body;
      
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: "usd",
        metadata: {
          eventId: eventId.toString(),
          userId: userId?.toString() || 'guest'
        }
      });

      res.json({ clientSecret: paymentIntent.client_secret });
    } catch (error: any) {
      res.status(500).json({ message: "Error creating payment intent: " + error.message });
    }
  });

  // Artist Merch
  app.get("/api/artists/:artistId/events/:eventId/merch", async (req, res) => {
    try {
      const artistId = parseInt(req.params.artistId);
      const eventId = parseInt(req.params.eventId);
      const merch = await storage.getArtistMerch(artistId, eventId);
      res.json(merch);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  });

  const httpServer = createServer(app);

  // WebSocket Server for real-time updates
  const wss = new WebSocketServer({ 
    server: httpServer, 
    path: '/ws' 
  });

  wss.on('connection', (ws) => {
    console.log('Client connected to WebSocket');

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        
        // Handle different message types
        if (data.type === 'subscribe') {
          // Subscribe client to specific event updates
          ws.eventId = data.eventId;
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });

    ws.on('close', () => {
      console.log('Client disconnected from WebSocket');
    });

    // Send initial connection confirmation
    ws.send(JSON.stringify({ type: 'connected' }));
  });

  // Simulate real-time queue updates every 10 seconds
  setInterval(async () => {
    try {
      const events = await storage.getActiveEvents();
      
      for (const event of events) {
        const currentStatus = await storage.getQueueStatus(event.id);
        if (currentStatus) {
          // Simulate queue changes
          const preparing = Math.max(0, currentStatus.preparing + Math.floor(Math.random() * 3) - 1);
          const ready = Math.max(0, currentStatus.ready + Math.floor(Math.random() * 2) - 1);
          const avgWaitTime = Math.max(5, Math.min(20, currentStatus.avgWaitTime + Math.floor(Math.random() * 6) - 3));

          await storage.updateQueueStatus(event.id, {
            preparing,
            ready,
            avgWaitTime
          });

          // Broadcast to all connected clients
          wss.clients.forEach((client) => {
            if (client.readyState === WebSocket.OPEN) {
              client.send(JSON.stringify({
                type: 'queueUpdate',
                eventId: event.id,
                preparing,
                ready,
                avgWaitTime
              }));
            }
          });
        }
      }
    } catch (error) {
      console.error('Queue update error:', error);
    }
  }, 10000);

  return httpServer;
}
