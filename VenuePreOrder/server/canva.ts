// =============================================================================
// CANVA APPS SDK INTEGRATION - DESIGN CREATION SERVICE
// =============================================================================
// This module handles Canva Apps SDK integration for in-app design creation
// and export functionality for the venue merch preorder platform.
//
// 10th Grade Level: This lets artists create t-shirt designs directly inside
// our app using Canva's design tools, then automatically saves the designs.
//
// College Level: Integration with Canva Apps SDK for embedded design creation,
// asset management, and automated export to Printful fulfillment pipeline.

import axios from 'axios';

// Validate required environment variables
if (!process.env.CANVA_APP_ID || !process.env.CANVA_CLIENT_SECRET) {
  throw new Error('Missing required Canva credentials: CANVA_APP_ID and CANVA_CLIENT_SECRET');
}

// =============================================================================
// CANVA APPS SDK CONFIGURATION
// =============================================================================

/**
 * Generate Canva Apps SDK configuration for frontend
 * @param userId - Current user ID
 * @param artistId - Artist ID for design association
 * @returns Canva SDK configuration object
 */
export function generateCanvaConfig(userId: string, artistId: string) {
  return {
    appId: process.env.CANVA_APP_ID,
    // Configuration for embedded Canva editor
    editor: {
      // Design dimensions for merchandise (standard t-shirt print area)
      dimensions: {
        width: 3000,  // 10 inches at 300 DPI
        height: 3600, // 12 inches at 300 DPI
        unit: 'px'
      },
      // Allowed design elements
      features: {
        text: true,
        images: true,
        shapes: true,
        backgrounds: false, // Transparent background for merch
        effects: true,
        animations: false   // Static designs for print
      },
      // Brand colors and assets (can be customized per artist)
      brand: {
        colors: [
          '#000000', // Black
          '#FFFFFF', // White
          '#FF0000', // Red
          '#0000FF', // Blue
          '#00FF00', // Green
          '#FFFF00', // Yellow
          '#FF00FF', // Magenta
          '#00FFFF'  // Cyan
        ],
        fonts: [
          'Arial',
          'Helvetica',
          'Times New Roman',
          'Courier New',
          'Impact',
          'Comic Sans MS'
        ]
      }
    },
    // User context for personalization
    user: {
      id: userId,
      artistId: artistId,
      permissions: ['create', 'edit', 'export']
    },
    // Export settings for Printful compatibility
    export: {
      format: 'PNG',
      quality: 'high',
      dpi: 300,
      transparentBackground: true
    }
  };
}

// =============================================================================
// DESIGN EXPORT HANDLING
// =============================================================================

/**
 * Process design export from Canva Apps SDK
 * @param exportData - Design export data from Canva
 * @param artistId - Artist who created the design
 * @param eventId - Event the design is for
 * @returns Processed design information
 */
export async function processCanvaExport(exportData: {
  designId: string;
  exportUrl: string;
  title: string;
  format: string;
  dimensions: {
    width: number;
    height: number;
  };
}, artistId: string, eventId: string) {
  try {
    // Download the design file from Canva
    const response = await axios.get(exportData.exportUrl, {
      responseType: 'arraybuffer'
    });
    
    const designBuffer = Buffer.from(response.data);
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `canva-design-${exportData.designId}-${timestamp}.${exportData.format.toLowerCase()}`;
    
    return {
      designId: exportData.designId,
      title: exportData.title,
      filename: filename,
      buffer: designBuffer,
      format: exportData.format,
      dimensions: exportData.dimensions,
      artistId: artistId,
      eventId: eventId,
      source: 'canva',
      createdAt: new Date().toISOString()
    };
  } catch (error: any) {
    console.error('Error processing Canva export:', error.message);
    throw new Error('Failed to process design export from Canva');
  }
}

// =============================================================================
// CANVA WEBHOOK HANDLING
// =============================================================================

/**
 * Process webhooks from Canva Apps SDK
 * @param webhookData - Webhook payload from Canva
 * @returns Processed webhook information
 */
export function processCanvaWebhook(webhookData: any) {
  const { type, timestamp, data } = webhookData;
  
  switch (type) {
    case 'design.exported':
      return {
        type: 'design_exported',
        designId: data.design.id,
        exportUrl: data.export.url,
        title: data.design.title,
        format: data.export.format,
        dimensions: data.export.dimensions,
        userId: data.user.id,
        timestamp: timestamp
      };
      
    case 'design.created':
      return {
        type: 'design_created',
        designId: data.design.id,
        title: data.design.title,
        userId: data.user.id,
        timestamp: timestamp
      };
      
    case 'design.updated':
      return {
        type: 'design_updated',
        designId: data.design.id,
        title: data.design.title,
        userId: data.user.id,
        timestamp: timestamp
      };
      
    default:
      return {
        type: 'unknown',
        originalType: type,
        data: data,
        timestamp: timestamp
      };
  }
}

// =============================================================================
// DESIGN TEMPLATES
// =============================================================================

/**
 * Create predefined design templates for different merchandise types
 * @param merchType - Type of merchandise (t-shirt, hoodie, mug, etc.)
 * @returns Template configuration for Canva
 */
export function createMerchTemplate(merchType: string) {
  const templates = {
    't-shirt': {
      dimensions: { width: 3000, height: 3600 },
      printArea: { width: 2400, height: 3000 },
      safeArea: { width: 2200, height: 2800 },
      description: 'Standard t-shirt design template'
    },
    'hoodie': {
      dimensions: { width: 3000, height: 3600 },
      printArea: { width: 2400, height: 3000 },
      safeArea: { width: 2200, height: 2800 },
      description: 'Hoodie design template'
    },
    'mug': {
      dimensions: { width: 2700, height: 1080 },
      printArea: { width: 2500, height: 900 },
      safeArea: { width: 2300, height: 800 },
      description: 'Coffee mug wrap-around design'
    },
    'poster': {
      dimensions: { width: 2400, height: 3600 },
      printArea: { width: 2200, height: 3400 },
      safeArea: { width: 2000, height: 3200 },
      description: 'Concert poster design template'
    },
    'sticker': {
      dimensions: { width: 1200, height: 1200 },
      printArea: { width: 1100, height: 1100 },
      safeArea: { width: 1000, height: 1000 },
      description: 'Square sticker design template'
    }
  };
  
  const template = templates[merchType as keyof typeof templates];
  
  if (!template) {
    throw new Error(`Unsupported merchandise type: ${merchType}`);
  }
  
  return {
    type: merchType,
    ...template,
    guidelines: {
      dpi: 300,
      colorMode: 'RGB',
      fileFormat: 'PNG',
      transparentBackground: true,
      bleed: 0.125, // 1/8 inch bleed for print
      notes: [
        'Keep important elements within the safe area',
        'Use high contrast colors for better print quality',
        'Avoid very thin lines (less than 1pt)',
        'Text should be at least 8pt for readability'
      ]
    }
  };
}

// =============================================================================
// ARTIST BRAND INTEGRATION
// =============================================================================

/**
 * Generate artist-specific brand assets for Canva integration
 * @param artistData - Artist information from Spotify
 * @returns Brand configuration for Canva
 */
export function generateArtistBrand(artistData: {
  id: string;
  name: string;
  genres: string[];
  images: Array<{ url: string; width: number; height: number }>;
  primaryColor?: string;
  secondaryColor?: string;
}) {
  // Generate color palette based on artist genres
  const genreColors = {
    'rock': ['#000000', '#FF0000', '#FFFFFF'],
    'pop': ['#FF69B4', '#00BFFF', '#FFD700'],
    'hip-hop': ['#000000', '#FFD700', '#FF0000'],
    'electronic': ['#00FFFF', '#FF00FF', '#00FF00'],
    'country': ['#8B4513', '#DAA520', '#FFFFFF'],
    'jazz': ['#4B0082', '#FFD700', '#000000'],
    'classical': ['#000080', '#C0C0C0', '#FFFFFF'],
    'alternative': ['#696969', '#FF4500', '#FFFFFF']
  };
  
  // Get colors based on primary genre
  const primaryGenre = artistData.genres[0]?.toLowerCase() || 'rock';
  const colors = genreColors[primaryGenre as keyof typeof genreColors] || genreColors.rock;
  
  return {
    artist: {
      id: artistData.id,
      name: artistData.name,
      image: artistData.images[0]?.url || null
    },
    colors: [
      artistData.primaryColor || colors[0],
      artistData.secondaryColor || colors[1],
      ...colors.slice(2),
      '#000000', // Always include black
      '#FFFFFF'  // Always include white
    ],
    fonts: [
      'Impact',      // Bold, attention-grabbing
      'Arial Black', // Strong, readable
      'Helvetica',   // Clean, modern
      'Times New Roman', // Classic, elegant
      'Courier New'  // Typewriter, vintage
    ],
    templates: {
      logoPlacement: {
        corner: { x: 50, y: 50, size: 200 },
        center: { x: 1500, y: 1800, size: 400 },
        bottom: { x: 1500, y: 3200, size: 300 }
      },
      textStyles: {
        headline: { size: 72, weight: 'bold', color: colors[0] },
        subheading: { size: 48, weight: 'normal', color: colors[1] },
        body: { size: 24, weight: 'normal', color: '#000000' }
      }
    }
  };
}

// =============================================================================
// DESIGN VALIDATION
// =============================================================================

/**
 * Validate design meets print requirements
 * @param designData - Design information
 * @returns Validation result with any issues
 */
export function validateDesignForPrint(designData: {
  dimensions: { width: number; height: number };
  format: string;
  dpi?: number;
  colorMode?: string;
}) {
  const issues: string[] = [];
  const warnings: string[] = [];
  
  // Check dimensions
  if (designData.dimensions.width < 1200 || designData.dimensions.height < 1200) {
    issues.push('Design dimensions too small for quality printing (minimum 1200x1200px)');
  }
  
  // Check DPI
  if (designData.dpi && designData.dpi < 300) {
    warnings.push('DPI below 300 may result in lower print quality');
  }
  
  // Check format
  if (!['PNG', 'JPG', 'JPEG', 'PDF'].includes(designData.format.toUpperCase())) {
    issues.push('Unsupported file format for printing');
  }
  
  // Check color mode
  if (designData.colorMode && designData.colorMode !== 'RGB') {
    warnings.push('CMYK color mode recommended for print, but RGB is acceptable');
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues,
    warnings: warnings,
    recommendations: [
      'Use 300 DPI for best print quality',
      'Keep important elements away from edges',
      'Use high contrast colors',
      'Test design on different background colors'
    ]
  };
}
