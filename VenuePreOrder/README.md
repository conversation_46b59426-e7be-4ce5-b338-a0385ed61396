# 🎪 Venue Preorder Platform

A comprehensive venue operations platform for food & merchandise pre-orders, pickups, and artist-side sales. Built with React, Node.js, PostgreSQL, and Stripe.

## ✨ Features

- **🛒 Pre-Event Ordering**: Customers can order food and merchandise before arriving at the venue
- **👥 Multi-Role Access**: Support for guests, artists, and administrators
- **⚡ Real-Time Updates**: Live order tracking and queue status updates
- **💳 Secure Payments**: Stripe integration for safe payment processing
- **📱 Responsive Design**: Works seamlessly on desktop and mobile devices
- **🎨 Artist Portal**: Artists can manage their merchandise and view sales
- **📊 Admin Dashboard**: Comprehensive order management and analytics
- **🔄 Queue Management**: Real-time order preparation tracking

## 🛠 Tech Stack

### Frontend
- **React 18.3.1** - Modern UI framework
- **TypeScript** - Type safety
- **Vite 5.4.14** - Fast build tool
- **TailwindCSS** - Utility-first CSS
- **Radix UI** - Accessible component library
- **Wouter** - Lightweight routing
- **TanStack Query** - Server state management
- **Zustand** - Client state management

### Backend
- **Node.js 18+** - JavaScript runtime
- **Express 4.21.2** - Web framework
- **TypeScript** - Type safety
- **Drizzle ORM** - Database toolkit
- **PostgreSQL** - Primary database
- **WebSocket** - Real-time communication
- **JWT** - Authentication
- **bcryptjs** - Password hashing

### External Services
- **Stripe** - Payment processing
- **Firebase** (optional) - Real-time features
- **Neon Database** - Serverless PostgreSQL

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Stripe account

### 1. Clone and Setup
```bash
# Extract the project files
# Navigate to the project directory
cd VenuePreOrder

# Run the setup script (Unix/Mac)
./setup.sh

# Or manually install dependencies
npm install
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your actual values
# See DEPLOYMENT_SETUP.md for detailed instructions
```

### 3. Set Up Database
```bash
# Push database schema
npm run db:push
```

### 4. Start Development
```bash
# Start the development server
npm run dev

# Visit http://localhost:5000
```

## 📋 Environment Variables

### Required
- `DATABASE_URL` - PostgreSQL connection string
- `STRIPE_SECRET_KEY` - Stripe secret key
- `VITE_STRIPE_PUBLIC_KEY` - Stripe public key
- `JWT_SECRET` - JWT signing secret
- `SESSION_SECRET` - Session secret

### Optional
- `VITE_FIREBASE_API_KEY` - Firebase API key
- `VITE_FIREBASE_PROJECT_ID` - Firebase project ID
- `VITE_FIREBASE_APP_ID` - Firebase app ID

See `.env.example` for the complete list.

## 🏗 Project Structure

```
VenuePreOrder/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Application pages
│   │   ├── hooks/          # Custom React hooks
│   │   └── lib/            # Utilities and configurations
├── server/                 # Express.js backend
│   ├── auth.ts            # Authentication logic
│   ├── db.ts              # Database connection
│   ├── routes.ts          # API routes
│   └── storage.ts         # Database operations
├── shared/                 # Shared types and schemas
├── uploads/               # File upload directory
├── .env.example           # Environment template
├── DEPLOYMENT_SETUP.md    # Detailed setup guide
└── setup.sh              # Automated setup script
```

## 🎯 User Roles

### 👤 Guests
- Browse events and menus
- Place pre-orders
- Track order status
- Make payments

### 🎨 Artists
- Manage merchandise
- View sales analytics
- Upload product images
- Track inventory

### 👑 Admins
- Manage all orders
- Update order status
- View comprehensive analytics
- Manage venues and events

## 🌐 Deployment

### Recommended Platforms
1. **Vercel** - Easy deployment with built-in PostgreSQL
2. **Railway** - Great for full-stack Node.js apps
3. **Render** - Good free tier option
4. **Replit** - Original development platform

### Deployment Steps
1. Choose a platform
2. Connect your repository
3. Add environment variables
4. Deploy!

See `DEPLOYMENT_SETUP.md` for detailed deployment instructions.

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run check        # TypeScript type checking

# Production
npm run build        # Build for production
npm start           # Start production server

# Database
npm run db:push     # Push schema to database
```

## 🗄 Database Schema

The application automatically creates these tables:
- `users` - User accounts and roles
- `venues` - Venue information
- `events` - Events at venues
- `menu_categories` - Food/merchandise categories
- `menu_items` - Individual items
- `orders` - Customer orders
- `order_items` - Items within orders
- `artist_merch` - Artist merchandise
- `queue_status` - Real-time order status

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Secure session management
- Role-based access control
- HTTPS support
- Input validation and sanitization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For setup help and troubleshooting:
1. Check `DEPLOYMENT_SETUP.md`
2. Review the `.env.example` file
3. Ensure all prerequisites are installed
4. Verify database connectivity
5. Check Stripe configuration

## 🎉 Getting Started

Ready to deploy your venue preorder platform? Follow these steps:

1. **Setup**: Run `./setup.sh` or follow manual setup
2. **Configure**: Edit `.env` with your credentials
3. **Database**: Run `npm run db:push`
4. **Deploy**: Choose your platform and deploy
5. **Test**: Place a test order to verify everything works

Happy coding! 🚀
