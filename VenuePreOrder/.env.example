# =============================================================================
# VENUE PREORDER PLATFORM - ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED)
# =============================================================================
# PostgreSQL Database URL - Get from your database provider
# Format: postgresql://username:password@host:port/database
DATABASE_URL=

# Alternative PostgreSQL configuration (if not using DATABASE_URL)
PGHOST=
PGPORT=5432
PGUSER=
PGPASSWORD=
PGDATABASE=

# =============================================================================
# STRIPE PAYMENT PROCESSING (REQUIRED)
# =============================================================================
# Stripe Public Key (starts with pk_test_ or pk_live_)
# Get from: https://dashboard.stripe.com/apikeys
VITE_STRIPE_PUBLIC_KEY=

# Stripe Secret Key (starts with sk_test_ or sk_live_) - KEEP SECRET!
# Get from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=

# =============================================================================
# FIREBASE CONFIGURATION (OPTIONAL - FOR REAL-TIME FEATURES)
# =============================================================================
# Firebase Project Configuration
# Get from: https://console.firebase.google.com -> Project Settings -> General
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_APP_ID=

# =============================================================================
# AUTHENTICATION & SECURITY (REQUIRED)
# =============================================================================
# JWT Secret for token signing (generate a random string)
# Use: openssl rand -base64 32
JWT_SECRET=

# Session Secret for express-session (generate a random string)
# Use: openssl rand -base64 32
SESSION_SECRET=

# =============================================================================
# REPLIT CONFIGURATION (IF DEPLOYING TO REPLIT)
# =============================================================================
# Replit-specific variables
REPL_ID=
REPLIT_DOMAINS=
ISSUER_URL=https://replit.com/oidc

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment mode (development, production)
NODE_ENV=production

# Server port (default: 5000)
PORT=5000

# =============================================================================
# OPTIONAL SERVICES
# =============================================================================
# File upload configuration (currently using local storage)
UPLOAD_DIR=uploads

# Email service (if implementing notifications)
EMAIL_SERVICE=
EMAIL_USER=
EMAIL_PASS=

# SMS service (if implementing SMS notifications)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
