import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { useCart } from '@/hooks/use-cart';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Clock, Package } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface MenuGridProps {
  eventId: number;
}

export default function MenuGrid({ eventId }: MenuGridProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('food');
  const { addItem } = useCart();
  const { toast } = useToast();

  const { data: menuData, isLoading } = useQuery({
    queryKey: [`/api/events/${eventId}/menu`],
    enabled: !!eventId
  });

  const handleAddToCart = (item: any) => {
    addItem({
      id: item.id,
      name: item.name,
      price: parseFloat(item.price),
      imageUrl: item.imageUrl
    });
    
    toast({
      title: "Added to cart",
      description: `${item.name} has been added to your cart.`,
    });
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="h-48 bg-gray-200 rounded-t-lg"></div>
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const categories = menuData || [];
  const foodCategories = categories.filter((cat: any) => cat.type === 'food');
  const drinkCategories = categories.filter((cat: any) => cat.type === 'drinks');
  const merchCategories = categories.filter((cat: any) => cat.type === 'merch');

  const renderItems = (items: any[]) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item) => (
        <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:scale-105">
          {item.imageUrl && (
            <div className="aspect-video relative overflow-hidden">
              <img 
                src={item.imageUrl} 
                alt={item.name}
                className="w-full h-full object-cover"
              />
              {item.stock <= 5 && item.stock > 0 && (
                <Badge variant="destructive" className="absolute top-2 right-2">
                  Low Stock
                </Badge>
              )}
              {item.stock === 0 && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                  <Badge variant="destructive">Out of Stock</Badge>
                </div>
              )}
            </div>
          )}
          
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle className="text-lg">{item.name}</CardTitle>
              <div className="flex items-center text-accent text-sm font-medium">
                <Clock className="w-4 h-4 mr-1" />
                {item.prepTime}min
              </div>
            </div>
            <p className="text-gray-600 text-sm">{item.description}</p>
          </CardHeader>
          
          <CardContent>
            <div className="flex justify-between items-center mb-4">
              <span className="text-2xl font-bold text-primary">${parseFloat(item.price).toFixed(2)}</span>
              <div className="flex items-center text-sm text-gray-500">
                <Package className="w-4 h-4 mr-1" />
                {item.stock} left
              </div>
            </div>
            
            <Button 
              className="w-full bg-primary hover:bg-primary/80"
              onClick={() => handleAddToCart(item)}
              disabled={item.stock === 0 || !item.isAvailable}
            >
              <i className="fas fa-plus mr-2"></i>
              Add to Cart
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">What's Available Tonight</h2>
        <p className="text-gray-600">Fresh food and exclusive merch ready for pickup</p>
      </div>

      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="food" className="flex items-center space-x-2">
            <i className="fas fa-utensils"></i>
            <span>Food</span>
          </TabsTrigger>
          <TabsTrigger value="drinks" className="flex items-center space-x-2">
            <i className="fas fa-wine-glass"></i>
            <span>Drinks</span>
          </TabsTrigger>
          <TabsTrigger value="merch" className="flex items-center space-x-2">
            <i className="fas fa-tshirt"></i>
            <span>Merch</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="food" className="mt-8">
          {foodCategories.map((category: any) => (
            <div key={category.id} className="mb-8">
              <h3 className="text-xl font-semibold mb-4">{category.name}</h3>
              {renderItems(category.items || [])}
            </div>
          ))}
        </TabsContent>

        <TabsContent value="drinks" className="mt-8">
          {drinkCategories.map((category: any) => (
            <div key={category.id} className="mb-8">
              <h3 className="text-xl font-semibold mb-4">{category.name}</h3>
              {renderItems(category.items || [])}
            </div>
          ))}
        </TabsContent>

        <TabsContent value="merch" className="mt-8">
          {merchCategories.map((category: any) => (
            <div key={category.id} className="mb-8">
              <h3 className="text-xl font-semibold mb-4">{category.name}</h3>
              {renderItems(category.items || [])}
            </div>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
