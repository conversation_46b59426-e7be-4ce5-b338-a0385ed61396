import { useState } from 'react';
import { useCart } from '@/hooks/use-cart';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Trash2, Plus, Minus } from 'lucide-react';
import { Link, useLocation } from 'wouter';

export default function CartWidget() {
  const { items, getTotalAmount, getTotalItems, updateQuantity, removeItem } = useCart();
  const [isOpen, setIsOpen] = useState(false);
  const [, setLocation] = useLocation();

  const totalItems = getTotalItems();
  const totalAmount = getTotalAmount();

  if (totalItems === 0) return null;

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button className="relative bg-primary hover:bg-primary/80 text-white">
          <i className="fas fa-shopping-cart mr-2"></i>
          <span className="hidden sm:inline">Cart</span>
          <Badge variant="secondary" className="absolute -top-2 -right-2 bg-secondary text-white">
            {totalItems}
          </Badge>
        </Button>
      </SheetTrigger>
      
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Your Order</SheetTitle>
        </SheetHeader>
        
        <div className="mt-6 space-y-4">
          {items.map((item) => (
            <div key={item.id} className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg">
              {item.imageUrl && (
                <img 
                  src={item.imageUrl} 
                  alt={item.name}
                  className="w-16 h-16 object-cover rounded-lg"
                />
              )}
              
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{item.name}</h4>
                <p className="text-sm text-gray-600">${item.price.toFixed(2)} each</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="w-8 text-center font-medium">{item.quantity}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeItem(item.id)}
                className="text-red-500 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
        
        <div className="mt-6 border-t pt-6">
          <div className="flex justify-between items-center mb-4">
            <span className="text-lg font-semibold">Total</span>
            <span className="text-2xl font-bold text-primary">${totalAmount.toFixed(2)}</span>
          </div>
          
          <Button 
            className="w-full bg-primary hover:bg-primary/80" 
            size="lg"
            onClick={() => {
              setIsOpen(false);
              setLocation('/guest/checkout');
            }}
          >
            Proceed to Checkout
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
}
