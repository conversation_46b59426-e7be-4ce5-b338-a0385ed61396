@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222 84% 4.9%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --primary: 247 90% 66%; /* #6366F1 - Indigo */
  --primary-foreground: 0 0% 98%;
  --secondary: 328 85% 70%; /* #EC4899 - Pink */
  --secondary-foreground: 0 0% 98%;
  --accent: 160 60% 45%; /* #10B981 - Emerald */
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 247 90% 66%;
  --radius: 0.75rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 247 90% 66%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 210 40% 96%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 247 90% 66%;
}

.dark {
  --background: 222 84% 4.9%; /* Dark navy */
  --foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --popover: 222 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --card: 222 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --primary: 247 90% 66%; /* #6366F1 - Indigo */
  --primary-foreground: 222 84% 4.9%;
  --secondary: 328 85% 70%; /* #EC4899 - Pink */
  --secondary-foreground: 222 84% 4.9%;
  --accent: 160 60% 45%; /* #10B981 - Emerald */
  --accent-foreground: 222 84% 4.9%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --ring: 247 90% 66%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --sidebar-background: 222 84% 4.9%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 247 90% 66%;
  --sidebar-primary-foreground: 222 84% 4.9%;
  --sidebar-accent: 217.2 32.6% 17.5%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 217.2 32.6% 17.5%;
  --sidebar-ring: 247 90% 66%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  /* Custom scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground;
  }

  /* Gradient text utilities */
  .gradient-text {
    @apply bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-subtle {
    animation: bounce 2s infinite;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Custom button variants */
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus-ring;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/90 focus-ring;
  }

  .btn-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90 focus-ring;
  }

  /* Card styling enhancements */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Status indicator colors */
  .status-confirmed {
    @apply bg-blue-100 text-blue-800 border-blue-200;
  }

  .status-preparing {
    @apply bg-yellow-100 text-yellow-800 border-yellow-200;
  }

  .status-ready {
    @apply bg-green-100 text-green-800 border-green-200;
  }

  .status-completed {
    @apply bg-gray-100 text-gray-800 border-gray-200;
  }

  /* Live indicator animation */
  .live-indicator {
    @apply relative;
  }

  .live-indicator::before {
    content: '';
    @apply absolute -inset-1 rounded-full bg-green-400 animate-ping;
  }

  /* Venue flow specific styles */
  .venue-banner {
    @apply bg-gradient-to-r from-primary to-purple-600 text-white;
    animation: slideInFromTop 0.5s ease-out;
  }

  @keyframes slideInFromTop {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Order status timeline */
  .order-timeline {
    @apply relative;
  }

  .order-timeline::before {
    content: '';
    @apply absolute top-6 left-6 right-6 h-0.5 bg-border;
    z-index: 0;
  }

  .order-timeline .progress-line {
    @apply absolute top-6 left-6 h-0.5 bg-gradient-to-r from-primary to-accent;
    z-index: 1;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .mobile-bottom-nav {
      @apply fixed bottom-0 left-0 right-0 bg-background border-t border-border z-50;
      padding-bottom: env(safe-area-inset-bottom);
    }
  }

  /* Real-time update flash */
  .flash-update {
    animation: flashGreen 0.5s ease-in-out;
  }

  @keyframes flashGreen {
    0%, 100% { background-color: transparent; }
    50% { background-color: rgb(34 197 94 / 0.1); }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
  }
}

/* Component-specific styles */
@layer components {
  .cart-widget {
    @apply fixed bottom-6 right-6 z-40 shadow-lg;
  }

  .order-card {
    @apply bg-card text-card-foreground rounded-lg border shadow-sm hover:shadow-md transition-shadow;
  }

  .queue-status {
    @apply bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4;
  }

  .portal-card {
    @apply bg-gradient-to-br rounded-2xl p-8 hover:shadow-xl transition-all transform hover:-translate-y-2 card-hover;
  }

  .stats-card {
    @apply bg-card rounded-lg p-6 border shadow-sm;
  }

  .menu-item-card {
    @apply bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border;
  }

  /* Dark mode adjustments */
  .dark .venue-banner {
    @apply from-primary/80 to-purple-600/80;
  }

  .dark .queue-status {
    @apply from-green-950/50 to-emerald-950/50 border-green-800;
  }
}

/* Utility classes for VenueFlow specific styling */
@layer utilities {
  .text-venue-primary {
    color: hsl(var(--primary));
  }

  .text-venue-secondary {
    color: hsl(var(--secondary));
  }

  .text-venue-accent {
    color: hsl(var(--accent));
  }

  .bg-venue-primary {
    background-color: hsl(var(--primary));
  }

  .bg-venue-secondary {
    background-color: hsl(var(--secondary));
  }

  .bg-venue-accent {
    background-color: hsl(var(--accent));
  }

  .border-venue-primary {
    border-color: hsl(var(--primary));
  }

  .border-venue-secondary {
    border-color: hsl(var(--secondary));
  }

  .border-venue-accent {
    border-color: hsl(var(--accent));
  }
}
