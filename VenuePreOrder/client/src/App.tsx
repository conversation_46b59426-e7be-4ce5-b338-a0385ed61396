import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

// Import pages
import Home from "@/pages/home";
import Login from "@/pages/auth/login";
import Register from "@/pages/auth/register";
import GuestOrder from "@/pages/guest/order";
import Checkout from "@/pages/guest/checkout";
import AdminDashboard from "@/pages/admin/dashboard";
import ArtistPortal from "@/pages/artist/portal";
import PreEventOrder from "@/pages/pre-event/order";
import MyOrders from "@/pages/my-orders";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      {/* Home page */}
      <Route path="/" component={Home} />
      
      {/* Authentication */}
      <Route path="/login" component={Login} />
      <Route path="/register" component={Register} />
      
      {/* Guest portal routes */}
      <Route path="/guest/order" component={GuestOrder} />
      <Route path="/guest/checkout" component={Checkout} />
      
      {/* Pre-event ordering */}
      <Route path="/pre-event/order" component={PreEventOrder} />
      <Route path="/my-orders" component={MyOrders} />
      
      {/* Admin portal routes */}
      <Route path="/admin/dashboard" component={AdminDashboard} />
      
      {/* Artist portal routes */}
      <Route path="/artist/portal" component={ArtistPortal} />
      
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
