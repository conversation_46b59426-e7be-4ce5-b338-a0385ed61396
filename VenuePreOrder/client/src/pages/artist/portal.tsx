import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Palette, Upload, Package, Plus, Image } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface MerchFormData {
  name: string;
  description: string;
  price: string;
  stock: number;
  eventId: number;
  imageFile: File | null;
}

export default function ArtistPortal() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<MerchFormData>({
    name: '',
    description: '',
    price: '',
    stock: 0,
    eventId: 1,
    imageFile: null
  });

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const { data: events } = useQuery({ queryKey: ['/api/events'] });
  const { data: merchandise } = useQuery({ 
    queryKey: ['/api/artist-merch', user.id] 
  });

  const addMerchMutation = useMutation({
    mutationFn: async (data: MerchFormData) => {
      let imageUrl = null;
      
      // Upload image first if provided
      if (data.imageFile) {
        setIsUploading(true);
        const formData = new FormData();
        formData.append('image', data.imageFile);
        
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formData
        });
        
        if (!uploadResponse.ok) {
          throw new Error('Failed to upload image');
        }
        
        const uploadResult = await uploadResponse.json();
        imageUrl = uploadResult.imageUrl;
        setIsUploading(false);
      }

      // Create merchandise
      return apiRequest('/api/artist-merch', {
        method: 'POST',
        body: JSON.stringify({
          name: data.name,
          description: data.description,
          price: data.price,
          stock: data.stock,
          eventId: data.eventId,
          artistId: user.id,
          imageUrl
        })
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Merchandise added successfully!"
      });
      queryClient.invalidateQueries({ queryKey: ['/api/artist-merch'] });
      setShowAddForm(false);
      setFormData({
        name: '',
        description: '',
        price: '',
        stock: 0,
        eventId: 1,
        imageFile: null
      });
    },
    onError: (error: any) => {
      setIsUploading(false);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'stock' || name === 'eventId' ? parseInt(value) : value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({
      ...prev,
      imageFile: file
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    addMerchMutation.mutate(formData);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <Palette className="h-8 w-8 text-purple-600" />
              Artist Portal
            </h1>
            <p className="text-gray-600 mt-2">Manage your merchandise and products</p>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={() => setShowAddForm(!showAddForm)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Merchandise
            </Button>
            <Button variant="outline" onClick={() => {
              localStorage.removeItem('token');
              localStorage.removeItem('user');
              window.location.reload();
            }}>
              Logout
            </Button>
          </div>
        </div>

        {/* Add Merchandise Form */}
        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Add New Merchandise
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Product Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="T-Shirt, Poster, etc."
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="price">Price ($)</Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={handleInputChange}
                      placeholder="25.00"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="stock">Stock Quantity</Label>
                    <Input
                      id="stock"
                      name="stock"
                      type="number"
                      value={formData.stock}
                      onChange={handleInputChange}
                      placeholder="100"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="eventId">Event</Label>
                    <select
                      id="eventId"
                      name="eventId"
                      value={formData.eventId}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      required
                    >
                      {events?.map((event: any) => (
                        <option key={event.id} value={event.id}>
                          {event.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Product description..."
                    rows={3}
                  />
                </div>
                
                <div>
                  <Label htmlFor="image">Product Image</Label>
                  <div className="flex items-center gap-4">
                    <Input
                      id="image"
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="flex-1"
                    />
                    {formData.imageFile && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <Image className="h-3 w-3" />
                        {formData.imageFile.name}
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    type="submit" 
                    disabled={addMerchMutation.isPending || isUploading}
                    className="flex items-center gap-2"
                  >
                    {isUploading ? (
                      <>
                        <Upload className="h-4 w-4 animate-spin" />
                        Uploading Image...
                      </>
                    ) : addMerchMutation.isPending ? (
                      'Adding...'
                    ) : (
                      <>
                        <Plus className="h-4 w-4" />
                        Add Merchandise
                      </>
                    )}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => setShowAddForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Merchandise List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Merchandise</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {merchandise?.map((item: any) => (
                <div key={item.id} className="border rounded-lg p-4 space-y-3">
                  {item.imageUrl && (
                    <img 
                      src={item.imageUrl} 
                      alt={item.name}
                      className="w-full h-48 object-cover rounded-md"
                    />
                  )}
                  <div>
                    <h3 className="font-semibold text-lg">{item.name}</h3>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-bold">${item.price}</span>
                    <Badge variant={item.stock > 0 ? 'default' : 'destructive'}>
                      {item.stock > 0 ? `${item.stock} in stock` : 'Out of stock'}
                    </Badge>
                  </div>
                </div>
              ))}
              {!merchandise?.length && (
                <div className="col-span-full text-center py-8 text-gray-500">
                  No merchandise found. Add your first item to get started!
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}