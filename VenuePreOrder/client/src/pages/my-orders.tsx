import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Clock, Edit, Package, Ticket, UtensilsCrossed, Shirt } from 'lucide-react';
import { Link } from 'wouter';

export default function MyOrders() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch user's pre-event orders
  const { data: preEventOrders = [], isLoading } = useQuery({
    queryKey: ['/api/pre-event-orders/my-orders'],
  });

  // Fetch user's live orders
  const { data: liveOrders = [] } = useQuery({
    queryKey: ['/api/orders/my-orders'],
  });

  const canModifyOrder = (order: any) => {
    if (!order.canModifyUntil) return false;
    return new Date() < new Date(order.canModifyUntil);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'confirmed': return 'default';
      case 'preparing': return 'secondary';
      case 'ready': return 'default';
      case 'completed': return 'secondary';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  const getItemTypeIcon = (itemType: string) => {
    switch (itemType) {
      case 'ticket': return <Ticket className="h-4 w-4" />;
      case 'food': return <UtensilsCrossed className="h-4 w-4" />;
      case 'merch': return <Shirt className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTimeUntilModification = (canModifyUntil: string) => {
    const now = new Date();
    const deadline = new Date(canModifyUntil);
    const diffMs = deadline.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Modification period expired';
    
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMins % 60}m remaining to modify`;
    }
    return `${diffMins}m remaining to modify`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading your orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">My Orders</h1>
          <p className="text-gray-600">
            Track your pre-event and live orders. Modify pre-event orders up to 30 minutes before pickup.
          </p>
        </div>

        <div className="grid gap-6">
          {/* Pre-Event Orders */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Pre-Event Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              {preEventOrders.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">You haven't placed any pre-event orders yet.</p>
                  <Link href="/pre-event/order">
                    <Button>Create Pre-Event Order</Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {preEventOrders.map((order: any) => (
                    <div key={order.id} className="border rounded-lg p-4 bg-white">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold">Order #{order.id}</h3>
                          <p className="text-sm text-gray-600">
                            Event: {order.event?.name || 'Unknown Event'}
                          </p>
                          <p className="text-sm text-gray-600">
                            Pickup: {formatDate(order.pickupTime)}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant={getStatusBadgeVariant(order.status)}>
                            {order.status}
                          </Badge>
                          <div className="text-lg font-bold mt-1">
                            ${order.totalAmount}
                          </div>
                        </div>
                      </div>

                      {/* Order Items */}
                      <div className="space-y-2 mb-4">
                        {order.items?.map((item: any, index: number) => (
                          <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                            {getItemTypeIcon(item.itemType)}
                            <div className="flex-1">
                              <span className="font-medium">{item.name || `${item.itemType} item`}</span>
                              <span className="text-sm text-gray-600 ml-2">
                                x{item.quantity} @ ${item.unitPrice}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Modification Status */}
                      <div className="flex justify-between items-center border-t pt-4">
                        <div className="text-sm text-gray-600">
                          {canModifyOrder(order) ? (
                            <span className="text-green-600">
                              {getTimeUntilModification(order.canModifyUntil)}
                            </span>
                          ) : (
                            <span className="text-gray-500">
                              Modification period expired
                            </span>
                          )}
                        </div>
                        <div className="space-x-2">
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                          {canModifyOrder(order) && order.status === 'pending' && (
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                              <Edit className="h-4 w-4 mr-1" />
                              Modify Order
                            </Button>
                          )}
                        </div>
                      </div>

                      {order.notes && (
                        <div className="mt-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                          <p className="text-sm">
                            <strong>Notes:</strong> {order.notes}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Live Orders */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Live Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              {liveOrders.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">No live orders from current events.</p>
                  <Link href="/guest/order">
                    <Button>Order Now</Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {liveOrders.map((order: any) => (
                    <div key={order.id} className="border rounded-lg p-4 bg-white">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold">Live Order #{order.id}</h3>
                          <p className="text-sm text-gray-600">
                            Pickup: {formatDate(order.pickupTime)}
                          </p>
                          <p className="text-sm text-gray-600">
                            Location: {order.pickupLocation || 'Main concession'}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant={getStatusBadgeVariant(order.status)}>
                            {order.status}
                          </Badge>
                          <div className="text-lg font-bold mt-1">
                            ${order.totalAmount}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2 mb-4">
                        {order.items?.map((item: any, index: number) => (
                          <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                            <UtensilsCrossed className="h-4 w-4" />
                            <div className="flex-1">
                              <span className="font-medium">{item.name}</span>
                              <span className="text-sm text-gray-600 ml-2">
                                x{item.quantity} @ ${item.unitPrice}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-between items-center border-t pt-4">
                        <div className="text-sm text-gray-600">
                          {order.status === 'preparing' && (
                            <span className="text-orange-600">
                              Estimated ready: {order.estimatedReadyTime || 'TBD'}
                            </span>
                          )}
                          {order.status === 'ready' && (
                            <span className="text-green-600">
                              Ready for pickup!
                            </span>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          Track Order
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}