import { useState } from 'react';
import { useLocation } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserCircle, Shield, Palette } from 'lucide-react';

interface LoginFormData {
  email: string;
  password: string;
}

export default function Login() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleLogin = async (role: string) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Store token
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));

      // Redirect based on user role
      if (data.user.role === 'admin') {
        setLocation('/admin/dashboard');
      } else if (data.user.role === 'artist') {
        setLocation('/artist/portal');
      } else {
        setLocation('/guest/order');
      }

      toast({
        title: "Login Successful",
        description: `Welcome back, ${data.user.firstName || data.user.email}!`,
      });
    } catch (error: any) {
      toast({
        title: "Login Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const LoginForm = ({ role, title, description }: { role: string; title: string; description: string }) => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Enter your email"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="Enter your password"
            required
          />
        </div>
        
        <Button 
          onClick={() => handleLogin(role)}
          disabled={isLoading || !formData.email || !formData.password}
          className="w-full"
        >
          {isLoading ? 'Signing In...' : `Sign In as ${title}`}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold">
            Access Portal
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="guest" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="guest" className="flex items-center gap-2">
                <UserCircle className="h-4 w-4" />
                Guest
              </TabsTrigger>
              <TabsTrigger value="admin" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Admin
              </TabsTrigger>
              <TabsTrigger value="artist" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Artist
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="guest" className="mt-6">
              <LoginForm 
                role="guest"
                title="Guest"
                description="Access the ordering system as a guest"
              />
            </TabsContent>
            
            <TabsContent value="admin" className="mt-6">
              <LoginForm 
                role="admin"
                title="Admin"
                description="Manage venues, events, and orders"
              />
            </TabsContent>
            
            <TabsContent value="artist" className="mt-6">
              <LoginForm 
                role="artist"
                title="Artist"
                description="Manage your merchandise and products"
              />
            </TabsContent>
          </Tabs>
          
          <div className="mt-6 text-center space-y-2">
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/register')}
              className="text-sm text-gray-600"
            >
              Don't have an account? Sign Up
            </Button>
            <br />
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/')}
              className="text-sm text-gray-600"
            >
              Back to Home
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}