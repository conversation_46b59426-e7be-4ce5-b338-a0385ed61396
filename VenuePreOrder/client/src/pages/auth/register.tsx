import { useState } from 'react';
import { useLocation } from 'wouter';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserCircle, Shield, Palette } from 'lucide-react';

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  role: string;
}

export default function Register() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<RegisterFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: 'guest'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleRegister = async (role: string) => {
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "Passwords do not match",
        variant: "destructive",
      });
      return;
    }

    if (formData.password.length < 6) {
      toast({
        title: "Password Too Short",
        description: "Password must be at least 6 characters",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          role
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Store token
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));

      // Redirect based on user role
      if (data.user.role === 'admin') {
        setLocation('/admin/dashboard');
      } else if (data.user.role === 'artist') {
        setLocation('/artist/portal');
      } else {
        setLocation('/guest/order');
      }

      toast({
        title: "Registration Successful",
        description: `Welcome ${data.user.firstName}! Your ${role} account has been created.`,
      });
    } catch (error: any) {
      toast({
        title: "Registration Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const RegisterForm = ({ role, title, description }: { role: string; title: string; description: string }) => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold">{title} Registration</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              placeholder="John"
              required
            />
          </div>
          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              placeholder="Doe"
              required
            />
          </div>
        </div>
        
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="<EMAIL>"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="At least 6 characters"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <Input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            placeholder="Confirm your password"
            required
          />
        </div>
        
        <Button 
          onClick={() => handleRegister(role)}
          disabled={isLoading || !formData.email || !formData.password || !formData.firstName}
          className="w-full"
        >
          {isLoading ? 'Creating Account...' : `Create ${title} Account`}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold">
            Create Account
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="guest" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="guest" className="flex items-center gap-2">
                <UserCircle className="h-4 w-4" />
                Guest
              </TabsTrigger>
              <TabsTrigger value="admin" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Admin
              </TabsTrigger>
              <TabsTrigger value="artist" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Artist
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="guest" className="mt-6">
              <RegisterForm 
                role="guest"
                title="Guest"
                description="Order food and merchandise for events"
              />
            </TabsContent>
            
            <TabsContent value="admin" className="mt-6">
              <RegisterForm 
                role="admin"
                title="Admin"
                description="Manage venues, events, and orders"
              />
            </TabsContent>
            
            <TabsContent value="artist" className="mt-6">
              <RegisterForm 
                role="artist"
                title="Artist"
                description="Manage your merchandise and products"
              />
            </TabsContent>
          </Tabs>
          
          <div className="mt-6 text-center space-y-2">
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/login')}
              className="text-sm text-gray-600"
            >
              Already have an account? Sign In
            </Button>
            <br />
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/')}
              className="text-sm text-gray-600"
            >
              Back to Home
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}