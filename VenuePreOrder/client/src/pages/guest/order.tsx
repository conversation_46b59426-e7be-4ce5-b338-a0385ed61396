import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UserCircle, ShoppingCart, Clock } from 'lucide-react';
import MenuGrid from '@/components/menu-grid';
import CartWidget from '@/components/cart-widget';
import VenueBanner from '@/components/venue-banner';

export default function GuestOrder() {
  const { data: events } = useQuery({ queryKey: ['/api/events'] });
  const [selectedEventId, setSelectedEventId] = useState(1);

  const activeEvents = events?.filter((event: any) => event.isActive) || [];
  const selectedEvent = activeEvents.find((event: any) => event.id === selectedEventId);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserCircle className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Guest Order Portal</h1>
                <p className="text-sm text-gray-600">Place your order for pickup</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <CartWidget />
              <Button variant="outline" onClick={() => {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.reload();
              }}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Event Selection */}
        {activeEvents.length > 1 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Select Event
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {activeEvents.map((event: any) => (
                  <div
                    key={event.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedEventId === event.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedEventId(event.id)}
                  >
                    <h3 className="font-semibold text-lg">{event.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      {new Date(event.startTime).toLocaleDateString()} - {new Date(event.endTime).toLocaleDateString()}
                    </p>
                    <div className="mt-2 flex justify-between items-center">
                      <Badge variant={event.isActive ? 'default' : 'secondary'}>
                        {event.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      {selectedEventId === event.id && (
                        <Badge variant="outline" className="bg-blue-100">
                          Selected
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Venue Information */}
        <VenueBanner venueId={selectedEvent?.venueId} />

        {/* Menu Section */}
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Menu - {selectedEvent?.name || 'Select an Event'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedEvent ? (
                <MenuGrid eventId={selectedEventId} />
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg">No active events available</p>
                  <p className="text-sm">Please check back later or contact support</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Order Information */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">Important Notes:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Orders must be placed before the event's order cutoff time</li>
                  <li>• Pickup times will be assigned based on preparation capacity</li>
                  <li>• Please arrive at your designated pickup time to avoid delays</li>
                  <li>• Payment is processed securely through Stripe</li>
                </ul>
              </div>
              
              {selectedEvent && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Event:</span> {selectedEvent.name}
                  </div>
                  <div>
                    <span className="font-medium">Order Cutoff:</span>{' '}
                    {new Date(selectedEvent.orderCutoff).toLocaleString()}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}