import { useQuery } from '@tanstack/react-query';
import { <PERSON> } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import VenueBanner from '@/components/venue-banner';
import { useRealtime } from '@/hooks/use-realtime';
import { ArrowRight, Clock, MapPin, Users, TrendingUp } from 'lucide-react';

export default function Home() {
  const { data: events } = useQuery({
    queryKey: ['/api/events']
  });

  const { queueStatus } = useRealtime({ eventId: 1 });

  const currentEvent = events?.[0]; // Assume first event is current

  return (
    <div className="min-h-screen bg-gray-50">
      <VenueBanner venueId={1} />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary to-purple-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Skip the Lines,<br/>
            <span className="text-yellow-300">Savor the Experience</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-purple-100 max-w-3xl mx-auto">
            Pre-order food & merchandise for events. Real-time pickup scheduling with live queue management.
          </p>
          
          {/* Live Queue Status */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-2 text-green-300 mb-4">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="font-medium">Live Queue Status</span>
            </div>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-warning">{queueStatus.preparing}</div>
                <div className="text-sm">Preparing</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-300">{queueStatus.ready}</div>
                <div className="text-sm">Ready</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-300">~{queueStatus.avgWaitTime}min</div>
                <div className="text-sm">Avg Wait</div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/login">
              <Button size="lg" className="bg-white text-primary hover:bg-gray-100 text-lg px-8 py-4">
                <i className="fas fa-shopping-cart mr-2"></i>
                Access Portal
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary text-lg px-8 py-4">
              <i className="fas fa-play mr-2"></i>
              Watch Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Current Event Info */}
      {currentEvent && (
        <section className="py-12 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <Card className="border-l-4 border-l-primary">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-2xl">{currentEvent.name}</CardTitle>
                    <div className="flex items-center space-x-4 text-gray-600 mt-2">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {new Date(currentEvent.startTime).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })} - {new Date(currentEvent.endTime).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        Madison Square Garden
                      </div>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Live Event
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">127</div>
                    <div className="text-gray-600">Active Orders</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">5 min</div>
                    <div className="text-gray-600">Avg Wait Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-600 mb-2">$2.4k</div>
                    <div className="text-gray-600">Today's Sales</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      )}

      {/* Portal Selection */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Three Powerful Portals</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Complete ecosystem for venues, guests, and artists</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Guest Portal */}
            <Card className="hover:shadow-xl transition-all transform hover:-translate-y-2 bg-gradient-to-br from-blue-50 to-indigo-100">
              <CardHeader>
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4 mx-auto">
                  <i className="fas fa-mobile-alt text-white text-2xl"></i>
                </div>
                <CardTitle className="text-center text-xl">Guest Portal</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Multi-auth login options
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Real-time menu browsing
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Dynamic pickup scheduling
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Live order tracking
                  </li>
                </ul>
                <Link href="/login">
                  <Button className="w-full bg-primary hover:bg-primary/80">
                    <Users className="w-4 h-4 mr-2" />
                    Access Guest Portal
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Admin Portal */}
            <Card className="hover:shadow-xl transition-all transform hover:-translate-y-2 bg-gradient-to-br from-amber-50 to-orange-100">
              <CardHeader>
                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <i className="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <CardTitle className="text-center text-xl">Admin Dashboard</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Real-time order management
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Live queue visualization
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Inventory tracking
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Capacity management
                  </li>
                </ul>
                <Link href="/login">
                  <Button className="w-full bg-yellow-500 hover:bg-yellow-600">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Access Admin Portal
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Artist Portal */}
            <Card className="hover:shadow-xl transition-all transform hover:-translate-y-2 bg-gradient-to-br from-purple-50 to-pink-100">
              <CardHeader>
                <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <i className="fas fa-palette text-white text-2xl"></i>
                </div>
                <CardTitle className="text-center text-xl">Artist Portal</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Merchandise management
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Bundle creation tools
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Sales analytics
                  </li>
                  <li className="flex items-center">
                    <i className="fas fa-check text-green-500 mr-3"></i>
                    Order notifications
                  </li>
                </ul>
                <Link href="/login">
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    <i className="fas fa-palette mr-2"></i>
                    Access Artist Portal
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Built for Scale</h2>
            <p className="text-xl text-gray-600">Modern tech stack for reliable, real-time performance</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-sync-alt text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Real-time Sync</h3>
              <p className="text-gray-600">Firebase Firestore with live listeners for instant order updates</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-key text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Multi-Auth</h3>
              <p className="text-gray-600">Email, magic link, phone, and guest mode with seamless upgrade flow</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-credit-card text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Stripe Payments</h3>
              <p className="text-gray-600">Secure payment processing with order confirmation</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-rose-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-bell text-white text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Smart Notifications</h3>
              <p className="text-gray-600">Multi-channel alerts via push, SMS, and email</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-primary to-purple-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Venue?</h2>
          <p className="text-xl mb-8 text-purple-100">
            Deploy your own white-labeled pre-order platform with our complete codebase
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="https://github.com/JesseniaTriumph/venue-preorder-platform" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center bg-white text-primary px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all transform hover:scale-105 shadow-lg"
            >
              <i className="fab fa-github mr-2"></i>
              View on GitHub
            </a>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-primary">
              <i className="fas fa-rocket mr-2"></i>
              Deploy to Replit
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
