
This prompt needs to:

Be modular

Include all requirements, dependencies, and folder structure

Provide step-by-step goals for the MVP

Be Replit-compatible (with clear package.json, Firebase/Supabase setup instructions, etc.)

Use plain language + technical specs so even a junior dev or AI can execute

✅ MASTER BUILD PROMPT FOR REPLIT
(Use this in Ghostwriter, Replit AI, or to onboard a human dev)

🧠 Prompt Start (Paste into Replit AI)

You are an expert full-stack engineer. I need you to help me build a modular venue operations platform for food & merch pre-orders, pickups, and artist-side sales using JavaScript (React + Node). The build should use:

React + Vite for frontends

Node/Express OR Firebase Functions for backend

Firestore OR Supabase as the database

Stripe for payments

OneSignal or Twilio for notifications

🔧 Architecture
Create this file/folder structure:

bash
Copy
Edit
/venue-preorder-platform/
├── /client/        → Guest-facing app (food/merch ordering, scheduling pickup)
├── /admin/         → FOH/admin view (order dashboard, inventory mgmt)
├── /artist/        → Artist portal (upload merch, view orders, bundles)
├── /backend/       → Express or Firebase functions (APIs)
├── /shared/        → Shared utils/components
🧪 MVP Goals – Phase 1
Start with:

A guest can:

Login with Firebase Auth

Browse dummy food + merch menu

Select a scheduled pickup time

Add to cart + pay via Stripe

Receive SMS or push notification when order is ready

Admin can:

See incoming orders in real time

Update order status to “Preparing” or “Ready”

Input limited-time inventory (e.g. “25 pretzels”)

Backend will:

Store orders + inventory in Firestore or Supabase

Provide REST API endpoints (start with /orders, /menu, /events)

Push notifications (optional for MVP) via OneSignal or Twilio

📦 Dependencies
Install the following:

For client-side apps:

bash
Copy
Edit
npm install react-router-dom axios firebase stripe react-toastify
For backend (if Express):

bash
Copy
Edit
npm install express cors dotenv firebase-admin stripe
If using Firebase Functions instead:

bash
Copy
Edit
firebase init functions
cd functions
npm install firebase-admin firebase-functions stripe
Notifications:

OneSignal (PWA setup) OR Twilio for SMS (backend only)

🔐 Environment Variables
Add a .env file (and ignore it in .gitignore) with:

env
Copy
Edit
REACT_APP_FIREBASE_API_KEY=yourkey
REACT_APP_FIREBASE_AUTH_DOMAIN=yourdomain
REACT_APP_FIREBASE_PROJECT_ID=yourproject
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=xxx
REACT_APP_FIREBASE_APP_ID=xxx

REACT_APP_STRIPE_PUBLISHABLE_KEY=xxx
STRIPE_SECRET_KEY=xxx
ONESIGNAL_APP_ID=xxx
💡 Additional Notes
Start with dummy data: 3 events, 5 food items, 3 merch items.

For pickup times, offer 15-minute time slots (dropdown).

Add a visual “order ready” queue for admin dashboard.

Style in TailwindCSS (if included) or minimal CSS.

🧠 Replit Plan
Scaffold folders in Replit Workspace.

Set up client/ with Vite React and Firebase Auth.

Set up backend/ with Express or Firebase Functions + order endpoints.

Mock admin/ with a basic dashboard showing orders from DB.

Create simple shared/ folder with utility helpers (e.g., fetch wrapper).

Include README.md in root with basic startup instructions.

⚙️ Run Commands
Client:

bash
Copy
Edit
cd client
npm run dev
Backend (if Express):

bash
Copy
Edit
cd backend
node index.js
✨ BONUS (If time allows)
Add artist merch bundle creation to /artist/

Add loyalty account tracking (simple user dashboard showing past orders)

Generate QR code at checkout to verify pickup

🚀 GOAL
Create an MVP for 1 venue + 1 event where:

1 guest places a food + merch order

Admin sees the order and marks it ready

Guest is notified and picks it up

This MVP will be expanded into a white-labeled system later.