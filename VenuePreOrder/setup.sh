#!/bin/bash

# =============================================================================
# Venue Preorder Platform - Setup Script
# =============================================================================

set -e  # Exit on any error

echo "🚀 Setting up Venue Preorder Platform..."
echo "========================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "   Please upgrade Node.js from: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
echo "   This includes React, Node.js libraries, Stripe, Supabase, and more..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
    echo "   📚 Installed libraries include:"
    echo "   - React 18.3.1 (Frontend framework)"
    echo "   - Express 4.21.2 (Backend server)"
    echo "   - Stripe 18.2.1 (Payment processing)"
    echo "   - Supabase (Real-time database)"
    echo "   - TailwindCSS (Styling)"
    echo "   - And 80+ other libraries!"
else
    echo "❌ Failed to install dependencies"
    echo "   Try running 'npm install' manually to see detailed error messages"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo ""
    echo "📝 Creating environment file..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env file from template"
        echo "⚠️  Please edit .env file with your actual values before running the application"
    else
        echo "❌ .env.example file not found"
        exit 1
    fi
else
    echo "✅ .env file already exists"
fi

# Check if uploads directory exists
if [ ! -d "uploads" ]; then
    echo ""
    echo "📁 Creating uploads directory..."
    mkdir -p uploads
    echo "✅ Created uploads directory"
fi

# Type check
echo ""
echo "🔍 Running TypeScript type check..."
npm run check

if [ $? -eq 0 ]; then
    echo "✅ TypeScript check passed"
else
    echo "❌ TypeScript check failed"
    echo "   Please fix TypeScript errors before proceeding"
    exit 1
fi

# Check environment variables
echo ""
echo "🔧 Checking environment configuration..."

# Source the .env file to check variables
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
fi

MISSING_VARS=()

# Check required variables
if [ -z "$DATABASE_URL" ] && [ -z "$PGHOST" ]; then
    MISSING_VARS+=("DATABASE_URL or PGHOST/PGUSER/PGPASSWORD/PGDATABASE")
fi

if [ -z "$STRIPE_SECRET_KEY" ]; then
    MISSING_VARS+=("STRIPE_SECRET_KEY")
fi

if [ -z "$VITE_STRIPE_PUBLIC_KEY" ]; then
    MISSING_VARS+=("VITE_STRIPE_PUBLIC_KEY")
fi

if [ -z "$JWT_SECRET" ]; then
    MISSING_VARS+=("JWT_SECRET")
fi

if [ -z "$SESSION_SECRET" ]; then
    MISSING_VARS+=("SESSION_SECRET")
fi

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo "❌ Missing required environment variables:"
    for var in "${MISSING_VARS[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "   Please edit .env file and add the missing variables"
    echo "   See DEPLOYMENT_SETUP.md for detailed instructions"
    exit 1
else
    echo "✅ All required environment variables are set"
fi

# Test database connection (if DATABASE_URL is set)
if [ ! -z "$DATABASE_URL" ]; then
    echo ""
    echo "🗄️  Testing database connection..."
    
    # Try to push database schema
    npm run db:push > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ Database connection successful and schema updated"
    else
        echo "⚠️  Database connection failed or schema update failed"
        echo "   Please check your DATABASE_URL and ensure the database is accessible"
        echo "   You can manually run 'npm run db:push' to test the connection"
    fi
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Review and update .env file with your actual values"
echo "2. Ensure your PostgreSQL database is running and accessible"
echo "3. Set up your Stripe account and add the API keys"
echo "4. Run 'npm run dev' to start the development server"
echo "5. Visit http://localhost:5000 to access the application"
echo ""
echo "For deployment instructions, see DEPLOYMENT_SETUP.md"
echo ""
echo "Happy coding! 🚀"
