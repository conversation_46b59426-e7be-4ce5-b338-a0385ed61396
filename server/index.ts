import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

const app = express();

// CRITICAL: Railway requires PORT from environment and 0.0.0.0 binding
const PORT = process.env.PORT || 3001;

// CORS setup for Vercel frontend
app.use(cors({
  origin: [
    'http://localhost:3000', // Local development
    'https://venue-preorder-frontend.vercel.app', // Your Vercel URL
    process.env.FRONTEND_URL // Environment variable
  ].filter(Boolean),
  credentials: true
}));

app.use(express.json());

// Health check endpoint (Railway requires this)
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({ message: 'Venue Preorder Backend API', status: 'running' });
});

// Your API routes go here
// app.use('/api', yourRoutes);

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// CRITICAL: Bind to 0.0.0.0, not localhost
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
