import {
  users, venues, events, menuCategories, menuItems, orders, orderItems,
  artistMerch, queueStatus, tickets, preEventOrders, preEventOrderItems,
  type User, type InsertUser, type Venue, type InsertVenue,
  type Event, type InsertEvent, type MenuCategory, type InsertMenuCategory,
  type MenuItem, type InsertMenuItem, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type ArtistMerch, type InsertArtistMerch,
  type QueueStatus, type InsertQueueStatus, type Ticket, type InsertTicket,
  type PreEventOrder, type InsertPreEventOrder, type PreEventOrderItem, type InsertPreEventOrderItem
} from "@shared/schema";
import { db } from "./db";
import { eq, and } from "drizzle-orm";

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined>;
  
  // Venues
  getVenue(id: number): Promise<Venue | undefined>;
  getActiveVenues(): Promise<Venue[]>;
  createVenue(venue: InsertVenue): Promise<Venue>;
  
  // Events
  getEvent(id: number): Promise<Event | undefined>;
  getActiveEvents(): Promise<Event[]>;
  getEventsByVenue(venueId: number): Promise<Event[]>;
  createEvent(event: InsertEvent): Promise<Event>;
  
  // Menu
  getMenuCategories(eventId: number): Promise<MenuCategory[]>;
  getMenuItems(categoryId: number): Promise<MenuItem[]>;
  getMenuItem(id: number): Promise<MenuItem | undefined>;
  createMenuItem(item: InsertMenuItem): Promise<MenuItem>;
  updateMenuItemStock(id: number, stock: number): Promise<MenuItem | undefined>;
  
  // Orders
  getOrder(id: number): Promise<Order | undefined>;
  getOrdersByUser(userId: number): Promise<Order[]>;
  getOrdersByEvent(eventId: number): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrderStatus(id: number, status: string): Promise<Order | undefined>;
  getOrderItems(orderId: number): Promise<OrderItem[]>;
  createOrderItem(item: InsertOrderItem): Promise<OrderItem>;
  
  // Artist Merch
  getArtistMerch(artistId: number, eventId: number): Promise<ArtistMerch[]>;
  getArtistMerchItem(id: number): Promise<ArtistMerch | undefined>;
  getEventMerch(eventId: number): Promise<ArtistMerch[]>;
  createArtistMerch(merch: InsertArtistMerch): Promise<ArtistMerch>;
  updateMerchAvailability(id: number, isAvailable: boolean, stock?: number): Promise<ArtistMerch | undefined>;
  
  // Queue Status
  getQueueStatus(eventId: number): Promise<QueueStatus | undefined>;
  updateQueueStatus(eventId: number, status: InsertQueueStatus): Promise<QueueStatus>;
  
  // Tickets
  getTickets(eventId: number): Promise<Ticket[]>;
  createTicket(ticket: InsertTicket): Promise<Ticket>;
  updateTicketAvailability(id: number, availableQuantity: number): Promise<Ticket | undefined>;
  
  // Pre-Event Orders
  getPreEventOrder(id: number): Promise<PreEventOrder | undefined>;
  getPreEventOrdersByUser(userId: number): Promise<PreEventOrder[]>;
  createPreEventOrder(order: InsertPreEventOrder): Promise<PreEventOrder>;
  updatePreEventOrder(id: number, updates: Partial<InsertPreEventOrder>): Promise<PreEventOrder | undefined>;
  canModifyPreEventOrder(id: number): Promise<boolean>;
  
  // Pre-Event Order Items
  getPreEventOrderItems(preEventOrderId: number): Promise<PreEventOrderItem[]>;
  createPreEventOrderItem(item: InsertPreEventOrderItem): Promise<PreEventOrderItem>;
  updatePreEventOrderItem(id: number, updates: Partial<InsertPreEventOrderItem>): Promise<PreEventOrderItem | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User> = new Map();
  private venues: Map<number, Venue> = new Map();
  private events: Map<number, Event> = new Map();
  private menuCategories: Map<number, MenuCategory> = new Map();
  private menuItems: Map<number, MenuItem> = new Map();
  private orders: Map<number, Order> = new Map();
  private orderItems: Map<number, OrderItem> = new Map();
  private artistMerch: Map<number, ArtistMerch> = new Map();
  private queueStatus: Map<number, QueueStatus> = new Map();
  
  private currentId = 1;

  constructor() {
    this.seedData();
  }

  private seedData() {
    // Create test user account
    const testUser: User = {
      id: 1,
      email: "<EMAIL>",
      passwordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
      role: "guest",
      firstName: "Test",
      lastName: "User",
      username: "testuser",
      isGuest: false,
      createdAt: new Date()
    };
    this.users.set(1, testUser);

    // Create sample venue
    const venue: Venue = {
      id: 1,
      name: "Madison Square Garden",
      address: "4 Pennsylvania Plaza, New York, NY 10001",
      wifiSSID: "MSG_Guest",
      isActive: true
    };
    this.venues.set(1, venue);

    // Create sample event
    const event: Event = {
      id: 1,
      venueId: 1,
      name: "Arctic Monkeys - Live Concert",
      startTime: new Date("2024-12-20T20:00:00Z"),
      endTime: new Date("2024-12-20T23:00:00Z"),
      orderCutoff: new Date("2024-12-20T21:45:00Z"),
      isActive: true
    };
    this.events.set(1, event);

    // Create menu categories
    const categories: MenuCategory[] = [
      { id: 1, name: "Food", type: "food", eventId: 1 },
      { id: 2, name: "Drinks", type: "drinks", eventId: 1 },
      { id: 3, name: "Merchandise", type: "merch", eventId: 1 }
    ];
    categories.forEach(cat => this.menuCategories.set(cat.id, cat));

    // Create menu items
    const items: MenuItem[] = [
      {
        id: 1,
        categoryId: 1,
        name: "Venue Burger",
        description: "Premium beef patty with local fixings and crispy fries",
        price: "14.99",
        stock: 23,
        prepTime: 8,
        imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd",
        isAvailable: true
      },
      {
        id: 2,
        categoryId: 1,
        name: "Venue Special Pizza",
        description: "Wood-fired pizza with locally sourced ingredients",
        price: "18.99",
        stock: 12,
        prepTime: 12,
        imageUrl: "https://images.unsplash.com/photo-1513104890138-7c749659a591",
        isAvailable: true
      },
      {
        id: 3,
        categoryId: 2,
        name: "Craft Beer Flight",
        description: "Selection of 4 local craft beers to sample",
        price: "16.99",
        stock: 5,
        prepTime: 3,
        imageUrl: "https://images.unsplash.com/photo-1608270586620-248524c67de9",
        isAvailable: true
      },
      {
        id: 4,
        categoryId: 3,
        name: "Arctic Monkeys Tour T-Shirt",
        description: "Official 2024 tour merchandise",
        price: "29.99",
        stock: 45,
        prepTime: 2,
        imageUrl: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab",
        isAvailable: true
      }
    ];
    items.forEach(item => this.menuItems.set(item.id, item));

    // Initialize queue status
    const queueStatus: QueueStatus = {
      id: 1,
      eventId: 1,
      preparing: 7,
      ready: 3,
      avgWaitTime: 12,
      updatedAt: new Date()
    };
    this.queueStatus.set(1, queueStatus);

    this.currentId = 5;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { 
      id, 
      createdAt: new Date(),
      role: insertUser.role ?? null,
      email: insertUser.email ?? null,
      phone: insertUser.phone ?? null,
      username: insertUser.username ?? null,
      firstName: insertUser.firstName ?? null,
      lastName: insertUser.lastName ?? null,
      passwordHash: insertUser.passwordHash ?? null,
      isGuest: insertUser.isGuest ?? null
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    
    const updatedUser: User = { 
      ...user, 
      ...updates,
      role: updates.role !== undefined ? updates.role : user.role,
      email: updates.email !== undefined ? updates.email : user.email,
      phone: updates.phone !== undefined ? updates.phone : user.phone,
      username: updates.username !== undefined ? updates.username : user.username,
      firstName: updates.firstName !== undefined ? updates.firstName : user.firstName,
      lastName: updates.lastName !== undefined ? updates.lastName : user.lastName,
      passwordHash: updates.passwordHash !== undefined ? updates.passwordHash : user.passwordHash,
      isGuest: updates.isGuest !== undefined ? updates.isGuest : user.isGuest
    };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async getVenue(id: number): Promise<Venue | undefined> {
    return this.venues.get(id);
  }

  async getActiveVenues(): Promise<Venue[]> {
    return Array.from(this.venues.values()).filter(venue => venue.isActive);
  }

  async createVenue(insertVenue: InsertVenue): Promise<Venue> {
    const id = this.currentId++;
    const venue: Venue = { ...insertVenue, id };
    this.venues.set(id, venue);
    return venue;
  }

  async getEvent(id: number): Promise<Event | undefined> {
    return this.events.get(id);
  }

  async getActiveEvents(): Promise<Event[]> {
    return Array.from(this.events.values()).filter(event => event.isActive);
  }

  async getEventsByVenue(venueId: number): Promise<Event[]> {
    return Array.from(this.events.values()).filter(event => event.venueId === venueId);
  }

  async createEvent(insertEvent: InsertEvent): Promise<Event> {
    const id = this.currentId++;
    const event: Event = { ...insertEvent, id };
    this.events.set(id, event);
    return event;
  }

  async getMenuCategories(eventId: number): Promise<MenuCategory[]> {
    return Array.from(this.menuCategories.values()).filter(cat => cat.eventId === eventId);
  }

  async getMenuItems(categoryId: number): Promise<MenuItem[]> {
    return Array.from(this.menuItems.values()).filter(item => item.categoryId === categoryId);
  }

  async getMenuItem(id: number): Promise<MenuItem | undefined> {
    return this.menuItems.get(id);
  }

  async createMenuItem(insertItem: InsertMenuItem): Promise<MenuItem> {
    const id = this.currentId++;
    const item: MenuItem = { ...insertItem, id };
    this.menuItems.set(id, item);
    return item;
  }

  async updateMenuItemStock(id: number, stock: number): Promise<MenuItem | undefined> {
    const item = this.menuItems.get(id);
    if (item) {
      const updatedItem = { ...item, stock };
      this.menuItems.set(id, updatedItem);
      return updatedItem;
    }
    return undefined;
  }

  async getOrder(id: number): Promise<Order | undefined> {
    return this.orders.get(id);
  }

  async getOrdersByUser(userId: number): Promise<Order[]> {
    return Array.from(this.orders.values()).filter(order => order.userId === userId);
  }

  async getOrdersByEvent(eventId: number): Promise<Order[]> {
    return Array.from(this.orders.values()).filter(order => order.eventId === eventId);
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const id = this.currentId++;
    const order: Order = { 
      ...insertOrder, 
      id, 
      createdAt: new Date(), 
      updatedAt: new Date(),
      qrCode: `VF-${new Date().getFullYear()}-${id.toString().padStart(4, '0')}`
    };
    this.orders.set(id, order);
    return order;
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const order = this.orders.get(id);
    if (order) {
      const updatedOrder = { ...order, status, updatedAt: new Date() };
      this.orders.set(id, updatedOrder);
      return updatedOrder;
    }
    return undefined;
  }

  async getOrderItems(orderId: number): Promise<OrderItem[]> {
    return Array.from(this.orderItems.values()).filter(item => item.orderId === orderId);
  }

  async createOrderItem(insertItem: InsertOrderItem): Promise<OrderItem> {
    const id = this.currentId++;
    const item: OrderItem = { ...insertItem, id };
    this.orderItems.set(id, item);
    return item;
  }

  async getArtistMerch(artistId: number, eventId: number): Promise<ArtistMerch[]> {
    return Array.from(this.artistMerch.values()).filter(
      merch => merch.artistId === artistId && merch.eventId === eventId
    );
  }

  async getArtistMerchItem(id: number): Promise<ArtistMerch | undefined> {
    return this.artistMerch.get(id);
  }

  async getEventMerch(eventId: number): Promise<ArtistMerch[]> {
    return Array.from(this.artistMerch.values()).filter(
      merch => merch.eventId === eventId
    );
  }

  async createArtistMerch(insertMerch: InsertArtistMerch): Promise<ArtistMerch> {
    const id = this.currentId++;
    const merch: ArtistMerch = { ...insertMerch, id };
    this.artistMerch.set(id, merch);
    return merch;
  }

  async updateMerchAvailability(id: number, isAvailable: boolean, stock?: number): Promise<ArtistMerch | undefined> {
    const merch = this.artistMerch.get(id);
    if (!merch) return undefined;

    const updatedMerch = {
      ...merch,
      isAvailable,
      ...(stock !== undefined && { stock })
    };
    this.artistMerch.set(id, updatedMerch);
    return updatedMerch;
  }

  async getQueueStatus(eventId: number): Promise<QueueStatus | undefined> {
    return Array.from(this.queueStatus.values()).find(status => status.eventId === eventId);
  }

  async updateQueueStatus(eventId: number, insertStatus: InsertQueueStatus): Promise<QueueStatus> {
    const existingStatus = await this.getQueueStatus(eventId);
    if (existingStatus) {
      const updatedStatus = { ...existingStatus, ...insertStatus, updatedAt: new Date() };
      this.queueStatus.set(existingStatus.id, updatedStatus);
      return updatedStatus;
    } else {
      const id = this.currentId++;
      const status: QueueStatus = { ...insertStatus, id, eventId, updatedAt: new Date() };
      this.queueStatus.set(id, status);
      return status;
    }
  }
}

// Database Storage Implementation
export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined> {
    const [user] = await db
      .update(users)
      .set(updates)
      .where(eq(users.id, id))
      .returning();
    return user || undefined;
  }

  async getVenue(id: number): Promise<Venue | undefined> {
    const [venue] = await db.select().from(venues).where(eq(venues.id, id));
    return venue || undefined;
  }

  async getActiveVenues(): Promise<Venue[]> {
    return await db.select().from(venues).where(eq(venues.isActive, true));
  }

  async createVenue(insertVenue: InsertVenue): Promise<Venue> {
    const [venue] = await db
      .insert(venues)
      .values(insertVenue)
      .returning();
    return venue;
  }

  async getEvent(id: number): Promise<Event | undefined> {
    const [event] = await db.select().from(events).where(eq(events.id, id));
    return event || undefined;
  }

  async getActiveEvents(): Promise<Event[]> {
    return await db.select().from(events).where(eq(events.isActive, true));
  }

  async getEventsByVenue(venueId: number): Promise<Event[]> {
    return await db.select().from(events).where(eq(events.venueId, venueId));
  }

  async createEvent(insertEvent: InsertEvent): Promise<Event> {
    const [event] = await db
      .insert(events)
      .values(insertEvent)
      .returning();
    return event;
  }

  async getMenuCategories(eventId: number): Promise<MenuCategory[]> {
    return await db.select().from(menuCategories).where(eq(menuCategories.eventId, eventId));
  }

  async getMenuItems(categoryId: number): Promise<MenuItem[]> {
    return await db.select().from(menuItems).where(eq(menuItems.categoryId, categoryId));
  }

  async getMenuItem(id: number): Promise<MenuItem | undefined> {
    const [item] = await db.select().from(menuItems).where(eq(menuItems.id, id));
    return item || undefined;
  }

  async createMenuItem(insertItem: InsertMenuItem): Promise<MenuItem> {
    const [item] = await db
      .insert(menuItems)
      .values(insertItem)
      .returning();
    return item;
  }

  async updateMenuItemStock(id: number, stock: number): Promise<MenuItem | undefined> {
    const [item] = await db
      .update(menuItems)
      .set({ stock })
      .where(eq(menuItems.id, id))
      .returning();
    return item || undefined;
  }

  async getOrder(id: number): Promise<Order | undefined> {
    const [order] = await db.select().from(orders).where(eq(orders.id, id));
    return order || undefined;
  }

  async getOrdersByUser(userId: number): Promise<Order[]> {
    return await db.select().from(orders).where(eq(orders.userId, userId));
  }

  async getOrdersByEvent(eventId: number): Promise<Order[]> {
    return await db.select().from(orders).where(eq(orders.eventId, eventId));
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const [order] = await db
      .insert(orders)
      .values(insertOrder)
      .returning();
    return order;
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const [order] = await db
      .update(orders)
      .set({ status, updatedAt: new Date() })
      .where(eq(orders.id, id))
      .returning();
    return order || undefined;
  }

  async getOrderItems(orderId: number): Promise<OrderItem[]> {
    return await db.select().from(orderItems).where(eq(orderItems.orderId, orderId));
  }

  async createOrderItem(insertItem: InsertOrderItem): Promise<OrderItem> {
    const [item] = await db
      .insert(orderItems)
      .values(insertItem)
      .returning();
    return item;
  }

  async getArtistMerch(artistId: number, eventId: number): Promise<ArtistMerch[]> {
    return await db
      .select()
      .from(artistMerch)
      .where(and(eq(artistMerch.artistId, artistId), eq(artistMerch.eventId, eventId)));
  }

  async getArtistMerchItem(id: number): Promise<ArtistMerch | undefined> {
    const [merch] = await db.select().from(artistMerch).where(eq(artistMerch.id, id));
    return merch || undefined;
  }

  async getEventMerch(eventId: number): Promise<ArtistMerch[]> {
    return await db
      .select()
      .from(artistMerch)
      .where(eq(artistMerch.eventId, eventId));
  }

  async createArtistMerch(insertMerch: InsertArtistMerch): Promise<ArtistMerch> {
    const [merch] = await db
      .insert(artistMerch)
      .values(insertMerch)
      .returning();
    return merch;
  }

  async updateMerchAvailability(id: number, isAvailable: boolean, stock?: number): Promise<ArtistMerch | undefined> {
    const updateData: any = { isAvailable };
    if (stock !== undefined) {
      updateData.stock = stock;
    }

    const [merch] = await db
      .update(artistMerch)
      .set(updateData)
      .where(eq(artistMerch.id, id))
      .returning();
    return merch || undefined;
  }

  async getQueueStatus(eventId: number): Promise<QueueStatus | undefined> {
    const [status] = await db.select().from(queueStatus).where(eq(queueStatus.eventId, eventId));
    return status || undefined;
  }

  async updateQueueStatus(eventId: number, insertStatus: InsertQueueStatus): Promise<QueueStatus> {
    const existing = await this.getQueueStatus(eventId);
    
    if (existing) {
      const [status] = await db
        .update(queueStatus)
        .set({ ...insertStatus, updatedAt: new Date() })
        .where(eq(queueStatus.eventId, eventId))
        .returning();
      return status;
    } else {
      const [status] = await db
        .insert(queueStatus)
        .values({ ...insertStatus, eventId, updatedAt: new Date() })
        .returning();
      return status;
    }
  }
}

export const storage = new DatabaseStorage();
