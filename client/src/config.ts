// API Configuration
export const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://venue-preorder-backend.vercel.app'  // Update this with your actual Vercel URL
  : 'http://localhost:3000';

export const API_ENDPOINTS = {
  auth: {
    login: `${API_BASE_URL}/api/auth/login`,
    register: `${API_BASE_URL}/api/auth/register`,
    spotify: `${API_BASE_URL}/api/auth/spotify`,
  },
  venues: `${API_BASE_URL}/api/venues`,
  orders: `${API_BASE_URL}/api/orders`,
  merch: `${API_BASE_URL}/api/merch`,
  canva: `${API_BASE_URL}/api/canva`,
  printful: `${API_BASE_URL}/api/printful`,
} as const;
