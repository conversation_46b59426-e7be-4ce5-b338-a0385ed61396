import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// Helper function to safely load optional plugins
async function loadOptionalPlugins() {
  const plugins = [];

  // Only load Replit plugins in development with REPL_ID
  if (process.env.NODE_ENV !== "production" && process.env.REPL_ID) {
    try {
      const runtimeErrorOverlay = await import("@replit/vite-plugin-runtime-error-modal");
      plugins.push(runtimeErrorOverlay.default());
    } catch (e) {
      console.log("Replit runtime error overlay not available");
    }

    try {
      const cartographer = await import("@replit/vite-plugin-cartographer");
      plugins.push(cartographer.cartographer());
    } catch (e) {
      console.log("Replit cartographer not available");
    }
  }

  return plugins;
}

export default defineConfig(async () => ({
  plugins: [
    react(),
    ...(await loadOptionalPlugins()),
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets"),
    },
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"],
    },
  },
}));
